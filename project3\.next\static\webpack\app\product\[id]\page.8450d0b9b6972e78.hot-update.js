"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// SEO Head Component\nfunction ProductSEOHead(param) {\n    let { product } = param;\n    var _product_DiscountPrice;\n    if (!product) return null;\n    const metaTitle = product.MetaTitle || \"\".concat(product.ProductName, \" - Medical Equipment\");\n    const metaDescription = product.MetaDescription || product.ShortDescription || \"Buy \".concat(product.ProductName, \" at the best price. High-quality medical equipment with fast delivery.\");\n    const metaKeywords = product.MetaKeywords || \"\".concat(product.ProductName, \", medical equipment, healthcare, \").concat(product.CategoryName || 'medical supplies');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: metaKeywords\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: \"product\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:url\",\n                content:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: \"summary_large_image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:amount\",\n                content: ((_product_DiscountPrice = product.DiscountPrice) === null || _product_DiscountPrice === void 0 ? void 0 : _product_DiscountPrice.toString()) || product.Price.toString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:currency\",\n                content: \"USD\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:availability\",\n                content: product.StockQuantity > 0 ? \"in stock\" : \"out of stock\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:condition\",\n                content: \"new\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"canonical\",\n                href:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSEOHead;\n// Helper function to construct image URL (moved outside component for reuse)\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetails(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetails.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetails.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetails.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetails.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetails.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Using the exact configuration provided\n            const data = JSON.stringify({\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            });\n            const config = {\n                method: \"post\",\n                maxBodyLength: Number.POSITIVE_INFINITY,\n                url: \"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                data: data\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_16__[\"default\"].request(config);\n            console.log(\"Product detail API response:\", response.data);\n            if (response.data && response.data.data) {\n                try {\n                    // Parse the response data\n                    const parsedData = JSON.parse(response.data.data);\n                    console.log(\"Parsed product data:\", parsedData);\n                    if (parsedData) {\n                        // The API might return an array with one item or a single object\n                        const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                        if (productData) {\n                            // Ensure AttributesJson is properly parsed if it's a string\n                            if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                try {\n                                    productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                } catch (e) {\n                                    console.error('Error parsing AttributesJson:', e);\n                                    productData.AttributesJson = [];\n                                }\n                            } else if (!productData.AttributesJson) {\n                                productData.AttributesJson = [];\n                            }\n                            console.log('Product data with attributes:', productData);\n                            setProduct(productData);\n                            // Set active image\n                            if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                            }\n                            // Handle comma-separated video links\n                            if (productData.VideoLink) {\n                                console.log(\"Video links found:\", productData.VideoLink);\n                                const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                setVideoLinks(processedLinks);\n                                setSelectedVideoIndex(0);\n                            }\n                            // Set initial quantity based on product minimum order quantity\n                            if (productData.OrderMinimumQuantity > 0) {\n                                setQuantity(productData.OrderMinimumQuantity);\n                            }\n                        } else {\n                            setError(\"No product data found\");\n                        }\n                    } else {\n                        setError(\"Invalid product data format\");\n                    }\n                } catch (parseError) {\n                    setError(\"Error parsing product data\");\n                    console.error(\"Error parsing product data:\", parseError);\n                }\n            } else {\n                setError(\"No data in API response\");\n            }\n        } catch (error) {\n            setError(\"Error fetching product details\");\n            console.error(\"Error fetching product:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetails.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetails.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetails.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetails.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetails.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetails.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetails.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetails.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetails.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetails.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 539,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 561,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 565,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 570,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 588,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 595,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 594,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products/category/\".concat(product.CategoryID),\n                                            children: product.CategoryName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 603,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 602,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 587,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 619,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 627,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 633,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 645,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 632,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 656,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 655,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex items-center\",\n                                children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 665,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"In Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 664,\n                                            columnNumber: 17\n                                        }, this),\n                                        product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"(\",\n                                                product.StockQuantity,\n                                                \" available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 669,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-red-600\",\n                                    children: \"Out of Stock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 673,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 661,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 697,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 696,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 713,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 707,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 706,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 695,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 688,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 682,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 727,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 678,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-4 text-sm font-medium\",\n                                                children: \"Quantity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: decrementQuantity,\n                                                        className: getButtonStyles('decrement'),\n                                                        disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                        \"aria-label\": \"Decrease quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 743,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 742,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: incrementQuantity,\n                                                        className: getButtonStyles('increment'),\n                                                        disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                        \"aria-label\": \"Increase quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 760,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 759,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 749,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Min: \",\n                                                    product.OrderMinimumQuantity\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 766,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Max: \",\n                                                    Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 772,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 733,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 781,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 780,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 732,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                );\n                                                // Show success toast\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                            } catch (error) {\n                                                console.error('Error adding to cart:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 844,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    // Add to wishlist\n                                                    wishlist.addToWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error('Error updating wishlist:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error('Failed to update wishlist. Please try again.');\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 878,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 880,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 885,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 848,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 911,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 912,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 793,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-wrap gap-2\",\n                                children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs\",\n                                        children: keyword.trim()\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 918,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 p-4 bg-gray-50 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"About This Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 929,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 928,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 626,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 616,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                            className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 946,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"specifications\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 947,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 948,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Shipping & Returns\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 949,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 945,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose max-w-none\",\n                                dangerouslySetInnerHTML: {\n                                    __html: product.FullDescription\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 954,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No description available for this product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 959,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 952,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"specifications\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__.ProductSpecifications, {\n                                attributes: product.AttributesJson,\n                                className: \"bg-white rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No specifications available for this product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5\n                                                ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                    }, star, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 979,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 977,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" out of 5\",\n                                                    product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \" • \",\n                                                            product.TotalReviews,\n                                                            \" review\",\n                                                            product.TotalReviews !== 1 ? 's' : ''\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, this) : null\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 989,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 976,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Customer Reviews\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 998,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-gray-500\",\n                                                    children: \"Reviews will be displayed here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1002,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 mb-4\",\n                                                        children: \"No reviews yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1008,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Be the first to review\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1009,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1007,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 997,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 975,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1020,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-primary mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1023,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"Fast Delivery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1024,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Estimated delivery time: \",\n                                                            product.EstimatedShippingDays || '3-5',\n                                                            \" business days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1022,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-6 w-6 text-primary mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1029,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"Easy Returns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1030,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Hassle-free returns within 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1028,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1021,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1019,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1018,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 939,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 938,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 584,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetails, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist\n    ];\n});\n_c1 = ProductDetails;\nfunction ProductPage() {\n    _s1();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = params.id;\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: \"Product ID not found\",\n            retry: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 1051,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductDetails, {\n        productId: id\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 1054,\n        columnNumber: 10\n    }, this);\n}\n_s1(ProductPage, \"+jVsTcECDRo3yq2d7EQxlN9Ixog=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c2 = ProductPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductSEOHead\");\n$RefreshReg$(_c1, \"ProductDetails\");\n$RefreshReg$(_c2, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});