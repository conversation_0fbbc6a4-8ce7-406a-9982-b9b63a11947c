"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// SEO Head Component\nfunction ProductSEOHead(param) {\n    let { product } = param;\n    var _product_DiscountPrice;\n    if (!product) return null;\n    const metaTitle = product.MetaTitle || \"\".concat(product.ProductName, \" - Medical Equipment\");\n    const metaDescription = product.MetaDescription || product.ShortDescription || \"Buy \".concat(product.ProductName, \" at the best price. High-quality medical equipment with fast delivery.\");\n    const metaKeywords = product.MetaKeywords || \"\".concat(product.ProductName, \", medical equipment, healthcare, \").concat(product.CategoryName || 'medical supplies');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: metaKeywords\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: \"product\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:url\",\n                content:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: \"summary_large_image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:amount\",\n                content: ((_product_DiscountPrice = product.DiscountPrice) === null || _product_DiscountPrice === void 0 ? void 0 : _product_DiscountPrice.toString()) || product.Price.toString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:currency\",\n                content: \"USD\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:availability\",\n                content: product.StockQuantity > 0 ? \"in stock\" : \"out of stock\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:condition\",\n                content: \"new\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"canonical\",\n                href:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSEOHead;\n// Helper function to construct image URL (moved outside component for reuse)\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetails(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetails.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetails.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetails.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetails.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetails.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Using the exact configuration provided\n            const data = JSON.stringify({\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            });\n            const config = {\n                method: \"post\",\n                maxBodyLength: Number.POSITIVE_INFINITY,\n                url: \"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                data: data\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_16__[\"default\"].request(config);\n            console.log(\"Product detail API response:\", response.data);\n            if (response.data && response.data.data) {\n                try {\n                    // Parse the response data\n                    const parsedData = JSON.parse(response.data.data);\n                    console.log(\"Parsed product data:\", parsedData);\n                    if (parsedData) {\n                        // The API might return an array with one item or a single object\n                        const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                        if (productData) {\n                            // Ensure AttributesJson is properly parsed if it's a string\n                            if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                try {\n                                    productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                } catch (e) {\n                                    console.error('Error parsing AttributesJson:', e);\n                                    productData.AttributesJson = [];\n                                }\n                            } else if (!productData.AttributesJson) {\n                                productData.AttributesJson = [];\n                            }\n                            console.log('Product data with attributes:', productData);\n                            setProduct(productData);\n                            // Set active image\n                            if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                            }\n                            // Handle comma-separated video links\n                            if (productData.VideoLink) {\n                                console.log(\"Video links found:\", productData.VideoLink);\n                                const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                setVideoLinks(processedLinks);\n                                setSelectedVideoIndex(0);\n                            }\n                            // Set initial quantity based on product minimum order quantity\n                            if (productData.OrderMinimumQuantity > 0) {\n                                setQuantity(productData.OrderMinimumQuantity);\n                            }\n                        } else {\n                            setError(\"No product data found\");\n                        }\n                    } else {\n                        setError(\"Invalid product data format\");\n                    }\n                } catch (parseError) {\n                    setError(\"Error parsing product data\");\n                    console.error(\"Error parsing product data:\", parseError);\n                }\n            } else {\n                setError(\"No data in API response\");\n            }\n        } catch (error) {\n            setError(\"Error fetching product details\");\n            console.error(\"Error fetching product:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetails.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetails.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetails.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetails.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetails.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetails.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetails.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetails.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetails.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetails.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 539,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 561,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 565,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 570,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSEOHead, {\n                product: product\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products\",\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 11\n                                }, this),\n                                product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/products/category/\".concat(product.CategoryID),\n                                                    children: product.CategoryName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbPage, {\n                                        children: product.ProductName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:w-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__.ProductMediaGallery, {\n                                    media: mediaItems,\n                                    className: \"w-full rounded-lg overflow-hidden\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold mb-2\",\n                                        children: product.ProductName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 17\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 ml-2\",\n                                                children: [\n                                                    \"(\",\n                                                    product.Rating || 0,\n                                                    \") \",\n                                                    product.TotalReviews || 0,\n                                                    \" reviews\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 11\n                                    }, this),\n                                    renderPrice(),\n                                    product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-sm max-w-none mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: product.ShortDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 flex items-center\",\n                                        children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"In Stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.StockQuantity,\n                                                        \" available)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-red-600\",\n                                            children: \"Out of Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 border-t border-gray-200 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: \"Product Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-4\",\n                                                children: \"Choose your preferences from the options below.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 13\n                                            }, this),\n                                            Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: Object.entries(groupedAttributes).map((param)=>{\n                                                    let [groupId, attributes] = param;\n                                                    var _attributes_, _attributes_1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: [\n                                                                    ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pl-4\",\n                                                                children: attributes.map((attr)=>{\n                                                                    const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                                    const isSelected = !!selectedAttributes[attrKey];\n                                                                    const isRadioGroup = attributes.length > 1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center h-5\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                                    id: \"attr-\".concat(attrKey),\n                                                                                    name: \"attr-group-\".concat(groupId),\n                                                                                    className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                                    checked: isSelected,\n                                                                                    onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 699,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 text-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"attr-\".concat(attrKey),\n                                                                                    className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                                    children: [\n                                                                                        attr.AttributeValueText,\n                                                                                        (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                            children: [\n                                                                                                \"(\",\n                                                                                                attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                                \"$\",\n                                                                                                attr.PriceAdjustment,\n                                                                                                \" \",\n                                                                                                attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, attrKey, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, \"attr-group-\".concat(groupId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"No additional product details available.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-4 text-sm font-medium\",\n                                                        children: \"Quantity:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: decrementQuantity,\n                                                                className: getButtonStyles('decrement'),\n                                                                disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                                \"aria-label\": \"Decrease quantity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: incrementQuantity,\n                                                                className: getButtonStyles('increment'),\n                                                                disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                                \"aria-label\": \"Increase quantity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Min: \",\n                                                            product.OrderMinimumQuantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 13\n                                            }, this),\n                                            product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                        backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: product.StockQuantity <= 0 || addingToCart,\n                                                onClick: ()=>{\n                                                    if (!product) return;\n                                                    setAddingToCart(true);\n                                                    try {\n                                                        // Get the first product image or use a placeholder\n                                                        const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                        // Get selected attributes\n                                                        const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                        // Add to cart using the cart context with attributes and adjusted price\n                                                        cart.addToCart({\n                                                            id: product.ProductId,\n                                                            name: product.ProductName,\n                                                            price: product.DiscountPrice || product.Price,\n                                                            discountPrice: product.DiscountPrice,\n                                                            image: productImage,\n                                                            originalPrice: product.Price\n                                                        }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                        );\n                                                        // Show success toast\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                                    } catch (error) {\n                                                        console.error('Error adding to cart:', error);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                                    } finally{\n                                                        setAddingToCart(false);\n                                                    }\n                                                },\n                                                children: [\n                                                    addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: addingToWishlist,\n                                                onClick: ()=>{\n                                                    if (!product) return;\n                                                    setAddingToWishlist(true);\n                                                    try {\n                                                        // Check if product is already in wishlist\n                                                        const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                        if (isAlreadyInWishlist) {\n                                                            // Remove from wishlist if already there\n                                                            wishlist.removeFromWishlist(product.ProductId);\n                                                            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                        } else {\n                                                            // Add to wishlist\n                                                            wishlist.addToWishlist(product.ProductId);\n                                                            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                        }\n                                                    } catch (error) {\n                                                        console.error('Error updating wishlist:', error);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error('Failed to update wishlist. Please try again.');\n                                                    } finally{\n                                                        setAddingToWishlist(false);\n                                                    }\n                                                },\n                                                children: [\n                                                    addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5\",\n                                                        fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only md:not-sr-only md:inline\",\n                                                        children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                                onClick: ()=>{\n                                                    if (navigator.share) {\n                                                        navigator.share({\n                                                            title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                            text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                            url: window.location.href\n                                                        }).catch((err)=>console.error(\"Error sharing:\", err));\n                                                    } else {\n                                                        // Fallback - copy to clipboard\n                                                        navigator.clipboard.writeText(window.location.href);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Product link copied to clipboard\");\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only md:not-sr-only md:inline\",\n                                                        children: \"Share\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 11\n                                    }, this),\n                                    product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900 mb-3\",\n                                                children: \"Product Tags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                        children: keyword.trim()\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"About This Product\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 935,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.MetaDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                            defaultValue: \"description\",\n                            className: \"w-full\",\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                    className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"description\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"specifications\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Specifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"reviews\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Reviews\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 957,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"shipping\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Shipping & Returns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"description\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            dangerouslySetInnerHTML: {\n                                                __html: product.FullDescription\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 italic\",\n                                            children: \"No description available for this product.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"specifications\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__.ProductSpecifications, {\n                                            attributes: product.AttributesJson,\n                                            className: \"bg-white rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 italic\",\n                                            children: \"No specifications available for this product.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"reviews\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                    className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                                }, star, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" out of 5\",\n                                                                product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \" • \",\n                                                                        product.TotalReviews,\n                                                                        \" review\",\n                                                                        product.TotalReviews !== 1 ? 's' : ''\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 23\n                                                                }, this) : null\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Customer Reviews\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1012,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center py-8 text-gray-500\",\n                                                                children: \"Reviews will be displayed here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1016,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 mb-4\",\n                                                                    children: \"No reviews yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: \"Be the first to review\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1011,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"shipping\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-primary mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1039,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: \"Fast Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Estimated delivery time: \",\n                                                                        product.EstimatedShippingDays || '3-5',\n                                                                        \" business days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1041,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1038,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-primary mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: \"Easy Returns\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Hassle-free returns within 30 days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1047,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"seo-info\",\n                                    className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Product Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1058,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        product.MetaTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-blue-900 mb-2\",\n                                                                    children: \"SEO Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1062,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: product.MetaTitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1063,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1061,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-green-900 mb-2\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1069,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: product.MetaDescription\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1070,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1068,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-purple-50 rounded-lg border border-purple-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-purple-900 mb-3\",\n                                                                    children: \"Related Keywords\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"bg-purple-100 text-purple-800 hover:bg-purple-200\",\n                                                                            children: keyword.trim()\n                                                                        }, index, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 1079,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1077,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1075,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-orange-50 rounded-lg border border-orange-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-orange-900 mb-2\",\n                                                                    children: \"Category\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1089,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    className: \"bg-orange-100 text-orange-800 hover:bg-orange-200\",\n                                                                    children: product.CategoryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1090,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1088,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.VendorName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50 rounded-lg border border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: \"Vendor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1098,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: product.VendorName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1099,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1097,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1059,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1057,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1056,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1055,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 947,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductDetails, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist\n    ];\n});\n_c1 = ProductDetails;\nfunction ProductPage() {\n    _s1();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = params.id;\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: \"Product ID not found\",\n            retry: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 1121,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductDetails, {\n        productId: id\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 1124,\n        columnNumber: 10\n    }, this);\n}\n_s1(ProductPage, \"+jVsTcECDRo3yq2d7EQxlN9Ixog=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c2 = ProductPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductSEOHead\");\n$RefreshReg$(_c1, \"ProductDetails\");\n$RefreshReg$(_c2, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});