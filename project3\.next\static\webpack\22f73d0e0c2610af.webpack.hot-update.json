{"c": ["app/layout", "app/product/[id]/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./app/product/[id]/page.tsx", "(app-pages-browser)/./app/product/[id]/product-error.tsx", "(app-pages-browser)/./app/product/[id]/product-loading.tsx", "(app-pages-browser)/./components/products/product-media-gallery.tsx", "(app-pages-browser)/./components/products/product-specifications.tsx", "(app-pages-browser)/./components/ui/tabs.tsx", "(app-pages-browser)/./node_modules/@radix-ui/react-roving-focus/dist/index.mjs", "(app-pages-browser)/./node_modules/@radix-ui/react-tabs/dist/index.mjs", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/image.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/pause.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/play.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js", "(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/video.js", "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cyhyasoft%5C%5CDownloads%5C%5Cec%5C%5C.NET%208%20Version%20-%20Latest%5C%5Cproject%5C%5Ccodemedical%5C%5Cproject3%5C%5Capp%5C%5Cproduct%5C%5C%5Bid%5D%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!"]}