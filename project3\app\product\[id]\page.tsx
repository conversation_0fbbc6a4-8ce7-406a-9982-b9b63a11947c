"use client"

import { useState, useEffect, useMemo, useCallback } from 'react'
import { useParams } from "next/navigation"
import Link from "next/link"
import axios from "axios"
import { Metadata } from 'next'
import { 
  ShoppingCart, 
  Heart, 
  Share2, 
  Star, 
  ChevronRight, 
  ChevronDown, 
  ArrowLeft, 
  Check,
  Truck, 
  RotateCcw, 
  Award, 
  Clock
} from "lucide-react"
import { Button } from "@/components/ui/button"
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { toast } from "sonner"
import { useCart } from "@/contexts/cart-context"
import { useWishlist } from "@/contexts/wishlist-context"
import { ProductSpecifications } from "@/components/products/product-specifications"
import { ProductMediaGallery } from "@/components/products/product-media-gallery"
import ProductLoading from "./product-loading"
import ProductError from "./product-error"

type MediaItem = {
  type: 'image' | 'video'
  url: string
  thumbnail?: string
  alt?: string
}

interface ProductImage {
  AttachmentID: number
  AttachmentName: string
  AttachmentURL: string
  ProductID: number
  IsPrimary?: boolean
}

interface ProductShippingMethod {
  ShippingMethodID: number
  ShippingMethodName: string
}

interface ProductAttribute {
  ProductID: number;
  ProductAttributeID: number;
  AttributeName: string;
  DisplayName: string;
  AttributeValueID: number;
  AttributeValueText: string;
  PriceAdjustment?: number;
  PriceAdjustmentType?: number;
}

interface Product {
  ProductId: number
  ProductName: string
  ShortDescription?: string
  FullDescription?: string
  Price: number
  DiscountPrice?: number
  OldPrice?: number
  PriceIQD?: number
  PointNo?: number
  StockQuantity: number
  IsBoundToStockQuantity: boolean
  DisplayStockQuantity: boolean
  MetaTitle?: string
  MetaKeywords?: string
  MetaDescription?: string
  VendorName?: string
  Rating: number
  TotalReviews?: number
  IsShippingFree: boolean
  IsReturnAble: boolean
  MarkAsNew: boolean
  OrderMaximumQuantity: number
  OrderMinimumQuantity: number
  EstimatedShippingDays: number
  IsDiscountAllowed: boolean
  ProductImagesJson: ProductImage[]
  ProductShipMethodsJson?: ProductShippingMethod[]
  CategoryID?: number
  CategoryName?: string
  VideoLink?: string
  AttributesJson?: ProductAttribute[]
}

interface ProductDetailsProps {
  productId: string
}

// Helper function to fetch product data for metadata
async function fetchProductForMetadata(productId: string): Promise<Product | null> {
  try {
    const data = JSON.stringify({
      requestParameters: {
        ProductId: Number.parseInt(productId, 10),
        recordValueJson: "[]",
      },
    });

    const config = {
      method: "post",
      maxBodyLength: Number.POSITIVE_INFINITY,
      url: "https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      data: data,
    };

    const response = await axios.request(config);

    if (response.data && response.data.data) {
      const parsedData = JSON.parse(response.data.data);
      const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;
      return productData || null;
    }
    return null;
  } catch (error) {
    console.error("Error fetching product for metadata:", error);
    return null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: { id: string } }): Promise<Metadata> {
  const product = await fetchProductForMetadata(params.id);

  if (!product) {
    return {
      title: 'Product Not Found - Medical Equipment',
      description: 'The requested product could not be found.',
    };
  }

  const metaTitle = product.MetaTitle || `${product.ProductName} - Medical Equipment`;
  const metaDescription = product.MetaDescription || product.ShortDescription || `Buy ${product.ProductName} at the best price. High-quality medical equipment with fast delivery.`;
  const metaKeywords = product.MetaKeywords || `${product.ProductName}, medical equipment, healthcare, ${product.CategoryName || 'medical supplies'}`;

  const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0
    ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL)
    : '/placeholder.svg?height=400&width=400';

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: metaKeywords,
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      type: 'website',
      images: [
        {
          url: productImage,
          width: 400,
          height: 400,
          alt: product.ProductName,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [productImage],
    },
    other: {
      'product:price:amount': (product.DiscountPrice || product.Price).toString(),
      'product:price:currency': 'USD',
      'product:availability': product.StockQuantity > 0 ? 'in stock' : 'out of stock',
      'product:condition': 'new',
    },
  };
}

// Helper function to construct image URL (moved outside component for reuse)
const constructImageUrl = (attachmentUrl: string | null) => {
  if (!attachmentUrl) return "/placeholder.svg?height=400&width=400"

  if (attachmentUrl.startsWith("http")) {
    return attachmentUrl
  }

  const baseUrl = "https://admin.codemedicalapps.com"
  const normalizedAttachmentUrl = attachmentUrl.startsWith("/") ? attachmentUrl : `/${attachmentUrl}`

  return `${baseUrl}${normalizedAttachmentUrl}`
}

function ProductDetails({ productId }: ProductDetailsProps) {
  const cart = useCart()
  const wishlist = useWishlist()

  const [product, setProduct] = useState<Product | null>(null)
  const [loading, setLoading] = useState<boolean>(true)
  const [quantity, setQuantity] = useState<number>(1)
  const [activeImage, setActiveImage] = useState<string>("")
  const [videoLinks, setVideoLinks] = useState<string[]>([])
  const [selectedVideoIndex, setSelectedVideoIndex] = useState<number>(0)
  const [addingToCart, setAddingToCart] = useState<boolean>(false)
  const [addingToWishlist, setAddingToWishlist] = useState<boolean>(false)
  const [error, setError] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<string>("description")
  const [isAnimating, setIsAnimating] = useState(false)
  const [animationType, setAnimationType] = useState<'increment' | 'decrement' | null>(null)
  const [selectedAttributes, setSelectedAttributes] = useState<{[key: string]: boolean}>(() => {
    // Initialize with first option selected for each attribute if none selected
    const initial: {[key: string]: boolean} = {};
    if (product?.AttributesJson) {
      product.AttributesJson.forEach(attr => {
        const key = `${attr.ProductAttributeID}_${attr.AttributeValueID}`;
        initial[key] = true; // Select first option by default
      });
    }
    return initial;
  })

  useEffect(() => {
    fetchProduct()
  }, [productId])

  const fetchProduct = async () => {
    setLoading(true)
    setError(null)
    try {
      // Using the exact configuration provided
      const data = JSON.stringify({
        requestParameters: {
          ProductId: Number.parseInt(productId, 10),
          recordValueJson: "[]",
        },
      })

      const config = {
        method: "post",
        maxBodyLength: Number.POSITIVE_INFINITY,
        url: "https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
        data: data,
      }

      const response = await axios.request(config)
      console.log("Product detail API response:", response.data)

      if (response.data && response.data.data) {
        try {
          // Parse the response data
          const parsedData = JSON.parse(response.data.data)
          console.log("Parsed product data:", parsedData)

          if (parsedData) {
            // The API might return an array with one item or a single object
            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData

            if (productData) {
              // Ensure AttributesJson is properly parsed if it's a string
              if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {
                try {
                  productData.AttributesJson = JSON.parse(productData.AttributesJson);
                } catch (e) {
                  console.error('Error parsing AttributesJson:', e);
                  productData.AttributesJson = [];
                }
              } else if (!productData.AttributesJson) {
                productData.AttributesJson = [];
              }

              console.log('Product data with attributes:', productData);
              setProduct(productData)

              // Set active image
              if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {
                const primaryImage =
                  productData.ProductImagesJson.find((img: { IsPrimary: boolean; AttachmentURL: string }) => img.IsPrimary) || productData.ProductImagesJson[0]
                setActiveImage(constructImageUrl(primaryImage.AttachmentURL))
              }

              // Handle comma-separated video links
              if (productData.VideoLink) {
                console.log("Video links found:", productData.VideoLink)
                const links = productData.VideoLink.split(",").map((link: string) => link.trim())
                const processedLinks = links.map((link: string) => constructVideoUrl(link))
                setVideoLinks(processedLinks)
                setSelectedVideoIndex(0)
              }

              // Set initial quantity based on product minimum order quantity
              if (productData.OrderMinimumQuantity > 0) {
                setQuantity(productData.OrderMinimumQuantity)
              }
            } else {
              setError("No product data found")
            }
          } else {
            setError("Invalid product data format")
          }
        } catch (parseError) {
          setError("Error parsing product data")
          console.error("Error parsing product data:", parseError)
        }
      } else {
        setError("No data in API response")
      }
    } catch (error) {
      setError("Error fetching product details")
      console.error("Error fetching product:", error)
    } finally {
      setLoading(false)
    }
  }



  const constructVideoUrl = (videoLink: string | null) => {
    if (!videoLink) return "";
    
    if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {
      return videoLink;
    }
    // For MP4 videos, use a proxy URL to handle CORS
    if (videoLink.startsWith('http')) {
      return `/api/video-proxy?url=${encodeURIComponent(videoLink)}`;
    }
    const baseUrl = "https://admin.codemedicalapps.com";
    const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : `/${videoLink}`;
    return `/api/video-proxy?url=${encodeURIComponent(`${baseUrl}${normalizedVideoLink}`)}`;
  }

  // Group attributes by ProductAttributeID
  const groupedAttributes = useMemo(() => {
    if (!product?.AttributesJson) return {};
    
    return product.AttributesJson.reduce((groups, attr) => {
      const groupId = attr.ProductAttributeID;
      if (!groups[groupId]) {
        groups[groupId] = [];
      }
      groups[groupId].push(attr);
      return groups;
    }, {} as Record<number, typeof product.AttributesJson>);
  }, [product?.AttributesJson]);

  // Handle attribute selection with conditional behavior
  const handleAttributeChange = (attr: ProductAttribute, isChecked: boolean, isRadioGroup: boolean) => {
    setSelectedAttributes(prev => {
      const newState = { ...prev };
      const attrKey = `${attr.ProductAttributeID}_${attr.AttributeValueID}`;
      
      // For radio groups, uncheck all other attributes in the same group
      if (isRadioGroup && isChecked) {
        Object.keys(prev).forEach(key => {
          if (key.startsWith(`${attr.ProductAttributeID}_`) && key !== attrKey) {
            newState[key] = false;
          }
        });
      }
      
      // Set the selected attribute
      // For checkboxes, toggle the state
      // For radio buttons, always set to true (since we already unset others if needed)
      newState[attrKey] = isRadioGroup ? true : !prev[attrKey];
      
      return newState;
    });
  };

  // Render price with all price-related information
  const renderPrice = () => {
    if (!product) return null;
    
    const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;
    const adjustedPrice = calculateAdjustedPrice();
    const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);
    
    return (
      <div className="mb-4">
        {/* Main Price */}
        <div className="flex items-baseline gap-2">
          <span className="text-3xl font-bold text-primary">
            ${showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)}
          </span>
          
          {/* Old Price */}
          {showDiscount && (
            <span className="text-lg text-gray-400 line-through">
              ${product.Price.toFixed(2)}
            </span>
          )}
          
          {/* Adjusted Price */}
          {showAdjustedPrice && !showDiscount && (
            <span className="text-lg text-gray-400 line-through">
              ${product.Price.toFixed(2)}
            </span>
          )}
          
          {/* Discount Badge */}
          {showDiscount && (
            <span className="ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded">
              {Math.round(((product.Price - (product.DiscountPrice || 0)) / product.Price) * 100)}% OFF
            </span>
          )}
        </div>
        
        {/* IQD Price */}
        {product.PriceIQD && (
          <div className="mt-1 text-lg font-medium text-gray-600">
            {product.PriceIQD.toLocaleString()} IQD
          </div>
        )}
        
        {/* Points */}
        {product.PointNo && product.PointNo > 0 && (
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800">
              Earn {product.PointNo} points
            </span>
          </div>
        )}
        
        {/* Old Price (if different from current price) */}
        {product.OldPrice && product.OldPrice > product.Price && (
          <div className="mt-1 text-sm text-gray-500">
            <span className="line-through">${product.OldPrice.toFixed(2)}</span>
            <span className="ml-2 text-green-600">
              {Math.round(((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice) * 100)}% OFF
            </span>
          </div>
        )}
      </div>
    );
  };

  // Calculate adjusted price based on selected attributes
  const calculateAdjustedPrice = useCallback(() => {
    if (!product) return 0;
    
    let adjustedPrice = product.Price;
    
    if (product.AttributesJson && product.AttributesJson.length > 0) {
      product.AttributesJson.forEach(attr => {
        const attrKey = `${attr.ProductAttributeID}_${attr.AttributeValueID}`;
        if (selectedAttributes[attrKey] && 
            typeof attr.PriceAdjustment === 'number' && 
            typeof attr.PriceAdjustmentType === 'number') {
          
          switch(attr.PriceAdjustmentType) {
            case 1: // Fixed amount
              adjustedPrice += attr.PriceAdjustment;
              break;
            case 2: // Percentage
              adjustedPrice += (product.Price * attr.PriceAdjustment) / 100;
              break;
          }
        }
      });
    }
    
    return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0
  }, [product, selectedAttributes]);
  
  const adjustedPrice = useMemo(() => calculateAdjustedPrice(), [calculateAdjustedPrice]);



  // Render product badges
  const renderBadges = () => {
    if (!product) return null;
    
    return (
      <div className="absolute top-4 left-4 z-10 flex flex-col gap-2">
        {product.IsDiscountAllowed && (
          <Badge className="bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1">
            SALE
          </Badge>
        )}
        {product.MarkAsNew && (
          <Badge className="bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1">
            NEW
          </Badge>
        )}
      </div>
    );
  }

  // Combine images and videos into a single media array for the gallery
  const mediaItems = useMemo<MediaItem[]>(() => {
    const items: MediaItem[] = []
    
    // Add product images
    if (product?.ProductImagesJson?.length) {
      product.ProductImagesJson.forEach(img => {
        items.push({
          type: 'image' as const,
          url: constructImageUrl(img.AttachmentURL),
          alt: product?.ProductName || 'Product image',
          thumbnail: constructImageUrl(img.AttachmentURL)
        })
      })
    }
    
    // Add videos
    videoLinks.forEach((videoUrl, index) => {
      items.push({
        type: 'video' as const,
        url: videoUrl,
        alt: `${product?.ProductName || 'Product'} - Video ${index + 1}`,
        thumbnail: activeImage || '' // Use the active image as video thumbnail
      })
    })
    
    return items
  }, [product, videoLinks, activeImage])

  const animateCounter = (type: 'increment' | 'decrement') => {
    setAnimationType(type);
    setIsAnimating(true);
    setTimeout(() => setIsAnimating(false), 300);
  };

  const incrementQuantity = () => {
    if (product) {
      const maxQuantity =
        product.OrderMaximumQuantity > 0
          ? Math.min(product.OrderMaximumQuantity, product.StockQuantity)
          : product.StockQuantity;
      if (quantity < maxQuantity) {
        setQuantity((prev) => prev + 1);
        animateCounter('increment');
      } else {
        // Visual feedback when max quantity is reached
        toast.info(`Maximum quantity of ${maxQuantity} reached`);
      }
    }
  };

  const decrementQuantity = () => {
    if (product) {
      const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;
      if (quantity > minQuantity) {
        setQuantity((prev) => prev - 1);
        animateCounter('decrement');
      } else {
        // Visual feedback when min quantity is reached
        toast.info(`Minimum quantity is ${minQuantity}`);
      }
    }
  };

  // Dynamic button styles based on state
  const getButtonStyles = (type: 'increment' | 'decrement') => {
    const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';
    
    if (type === 'increment') {
      const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? 
        Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);
      return `${baseStyles} ${isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50'}`;
    } else {
      const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);
      return `${baseStyles} ${isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50'}`;
    }
  };

  // Counter display with animation
  const CounterDisplay = () => (
    <div className="relative flex items-center justify-center w-16">
      <span 
        className={`text-lg font-medium transition-all duration-200 ${
          isAnimating ? 'scale-125 text-primary' : 'scale-100'
        }`}
      >
        {quantity}
      </span>
      {isAnimating && (
        <span 
          className={`absolute text-xs font-bold text-primary transition-all duration-200 ${
            animationType === 'increment' ? '-top-6' : 'top-6'
          }`}
        >
          {animationType === 'increment' ? '+1' : '-1'}
        </span>
      )}
    </div>
  );

  // Early return if product is not loaded yet
  if (loading) {
    return <ProductLoading />
  }

  if (error) {
    return <ProductError error={error} retry={fetchProduct} />
  }

  if (!product) {
    return (
      <div className="container mx-auto px-4 py-8 text-center">
        <h1 className="text-2xl font-bold mb-4">Product Not Found</h1>
        <p className="mb-6">The product you are looking for could not be found.</p>
        <Link href="/products">
          <Button>
            <ArrowLeft className="mr-2 h-4 w-4" />
            View All Products
          </Button>
        </Link>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden">
        {/* Breadcrumb */}
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/">Home</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink asChild>
              <Link href="/products">Products</Link>
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          {product.CategoryName && (
            <>
              <BreadcrumbItem>
                <BreadcrumbLink asChild>
                  <Link href={`/products/category/${product.CategoryID}`}>{product.CategoryName}</Link>
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
            </>
          )}
          <BreadcrumbItem>
            <BreadcrumbPage>{product.ProductName}</BreadcrumbPage>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex flex-col lg:flex-row gap-8">
        {/* Product Media Gallery */}
        <div className="lg:w-1/2">
          <ProductMediaGallery 
            media={mediaItems}
            className="w-full rounded-lg overflow-hidden"
          />
        </div>

        {/* Product Details */}
        <div className="md:w-1/2">
          <h1 className="text-xl font-bold mb-2">{product.ProductName}</h1>



          {/* Rating */}
          <div className="flex items-center mb-4">
            <div className="flex">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={`w-4 h-4 ${
                    i < Math.floor(product.Rating || 0) 
                      ? "text-yellow-400 fill-yellow-400" 
                      : "text-gray-300"
                  }`}
                />
              ))}
            </div>
            <span className="text-sm text-gray-500 ml-2">
              ({product.Rating || 0}) {product.TotalReviews || 0} reviews
            </span>
          </div>

          {/* Price */}
          {renderPrice()}

          {/* Short Description */}
          {product.ShortDescription && (
            <div className="prose prose-sm max-w-none mb-6">
              <p>{product.ShortDescription}</p>
            </div>
          )}

          {/* Stock */}
          <div className="mb-4 flex items-center">
            {product.StockQuantity > 0 ? (
              <>
                <div className="flex items-center text-green-600">
                  <Check className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">In Stock</span>
                </div>
                {product.DisplayStockQuantity && (
                  <span className="text-sm text-gray-500 ml-2">({product.StockQuantity} available)</span>
                )}
              </>
            ) : (
              <span className="text-sm font-medium text-red-600">Out of Stock</span>
            )}
          </div>

          {/* Product attributes with radio button groups */}
          <div className="mb-6 border-t border-gray-200 pt-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Product Details</h3>
            <p className="text-sm text-gray-600 mb-4">Choose your preferences from the options below.</p>
            {Object.entries(groupedAttributes).length > 0 ? (
              <div className="space-y-6">
                {Object.entries(groupedAttributes).map(([groupId, attributes]) => (
                  <div key={`attr-group-${groupId}`} className="space-y-2">
                    <h4 className="text-sm font-medium text-gray-700">
                      {attributes[0]?.DisplayName || attributes[0]?.AttributeName}:
                    </h4>
                    <div className="space-y-2 pl-4">
                      {attributes.map((attr) => {
                        const attrKey = `${attr.ProductAttributeID}_${attr.AttributeValueID}`;
                        const isSelected = !!selectedAttributes[attrKey];
                        const isRadioGroup = attributes.length > 1;
                        
                        return (
                          <div key={attrKey} className="flex items-start">
                            <div className="flex items-center h-5">
                              <input
                                type={isRadioGroup ? "radio" : "checkbox"}
                                id={`attr-${attrKey}`}
                                name={`attr-group-${groupId}`}
                                className={`h-4 w-4 ${isRadioGroup ? 'rounded-full' : 'rounded'} border-gray-300 text-primary focus:ring-primary`}
                                checked={isSelected}
                                onChange={(e) => handleAttributeChange(attr, e.target.checked, isRadioGroup)}
                              />
                            </div>
                            <div className="ml-3 text-sm">
                              <label 
                                htmlFor={`attr-${attrKey}`}
                                className={`font-medium ${isSelected ? 'text-primary' : 'text-gray-700'}`}
                              >
                                {attr.AttributeValueText}
                                {(attr.PriceAdjustment || attr.PriceAdjustment === 0) && (
                                  <span className="ml-2 text-sm font-normal text-green-600">
                                    ({attr.PriceAdjustmentType === 1 ? '+' : ''}${attr.PriceAdjustment} {attr.PriceAdjustmentType === 2 ? '%' : ''})
                                  </span>
                                )}
                              </label>
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-sm text-gray-500">No additional product details available.</p>
            )}
          </div>

          {/* Quantity Selector */}
          <div className="mb-6">
            <div className="flex items-center">
              <span className="mr-4 text-sm font-medium">Quantity:</span>
              <div className="flex items-center space-x-1">
                <button
                  onClick={decrementQuantity}
                  className={getButtonStyles('decrement')}
                  disabled={quantity <= (product.OrderMinimumQuantity || 1)}
                  aria-label="Decrease quantity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                  </svg>
                </button>
                
                <CounterDisplay />
                
                <button
                  onClick={incrementQuantity}
                  className={getButtonStyles('increment')}
                  disabled={
                    product.OrderMaximumQuantity > 0
                      ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity)
                      : quantity >= product.StockQuantity
                  }
                  aria-label="Increase quantity"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
              
              {product.OrderMinimumQuantity > 1 && (
                <span className="ml-3 text-xs text-gray-500">
                  Min: {product.OrderMinimumQuantity}
                </span>
              )}
              
              {product.OrderMaximumQuantity > 0 && (
                <span className="ml-3 text-xs text-gray-500">
                  Max: {Math.min(product.OrderMaximumQuantity, product.StockQuantity)}
                </span>
              )}
            </div>
            
            {/* Stock indicator */}
            {product.DisplayStockQuantity && product.StockQuantity > 0 && (
              <div className="mt-2 w-full bg-gray-200 rounded-full h-2.5">
                <div 
                  className="bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out" 
                  style={{
                    width: `${Math.min(100, (quantity / product.StockQuantity) * 100)}%`,
                    backgroundColor: quantity > (product.StockQuantity * 0.8) ? '#ef4444' : '#10b981'
                  }}
                />
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-wrap gap-4">
            {/* Add to Cart Button */}
            <button
              type="button"
              className="flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none"
              disabled={product.StockQuantity <= 0 || addingToCart}
              onClick={() => {
                if (!product) return;

                setAddingToCart(true);
                try {
                  // Get the first product image or use a placeholder
                  const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0
                    ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL)
                    : '/placeholder.jpg';

                  // Get selected attributes
                  const selectedAttrs = (product.AttributesJson || []).filter(attr => 
                    selectedAttributes[`${attr.ProductAttributeID}_${attr.AttributeValueID}`]
                  );

                  // Add to cart using the cart context with attributes and adjusted price
                  cart.addToCart(
                    {
                      id: product.ProductId,
                      name: product.ProductName,
                      price: product.DiscountPrice || product.Price, // Use discount price if available
                      discountPrice: product.DiscountPrice,
                      image: productImage,
                      originalPrice: product.Price, // Always store the original price
                    },
                    quantity,
                    selectedAttrs,
                    product.PriceIQD // Pass IQD price as the fourth parameter
                  );

                  // Show success toast
                  toast.success(`${quantity} × ${product.ProductName} added to your cart`);
                } catch (error) {
                  console.error('Error adding to cart:', error);
                  toast.error("Failed to add product to cart. Please try again.");
                } finally {
                  setAddingToCart(false);
                }
              }}
            >
              {addingToCart ? (
                <div className="h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <ShoppingCart className="h-5 w-5" />
              )}
              <span>{addingToCart ? "Adding..." : "Add to Cart"}</span>
            </button>

            {/* Wishlist Button */}
            <button
              type="button"
              className="flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none"
              disabled={addingToWishlist}
              onClick={() => {
                if (!product) return;

                setAddingToWishlist(true);
                try {
                  // Check if product is already in wishlist
                  const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);

                  if (isAlreadyInWishlist) {
                    // Remove from wishlist if already there
                    wishlist.removeFromWishlist(product.ProductId);
                    toast.success(`${product.ProductName} removed from wishlist`);
                  } else {
                    // Add to wishlist
                    wishlist.addToWishlist(product.ProductId);
                    toast.success(`${product.ProductName} added to wishlist`);
                  }
                } catch (error) {
                  console.error('Error updating wishlist:', error);
                  toast.error('Failed to update wishlist. Please try again.');
                } finally {
                  setAddingToWishlist(false);
                }
              }}
            >
              {addingToWishlist ? (
                <div className="h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
              ) : (
                <Heart
                  className="h-5 w-5"
                  fill={product && wishlist.isInWishlist(product.ProductId) ? "currentColor" : "none"}
                />
              )}
              <span className="sr-only md:not-sr-only md:inline">
                {addingToWishlist ? "Updating..." :
                 (product && wishlist.isInWishlist(product.ProductId)) ? "Remove" : "Wishlist"}
              </span>
            </button>

            {/* Share Button */}
            <button
              type="button"
              className="flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground"
              onClick={() => {
                if (navigator.share) {
                  navigator
                    .share({
                      title: product?.MetaTitle || product?.ProductName,
                      text: product?.MetaDescription || `Check out this product: ${product?.ProductName}`,
                      url: window.location.href,
                    })
                    .catch((err) => console.error("Error sharing:", err))
                } else {
                  // Fallback - copy to clipboard
                  navigator.clipboard.writeText(window.location.href)
                  toast.success("Product link copied to clipboard")
                }
              }}
            >
              <Share2 className="h-5 w-5" />
              <span className="sr-only md:not-sr-only md:inline">Share</span>
            </button>
          </div>

          {/* Meta Keywords */}
          {product.MetaKeywords && (
            <div className="mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
              <h3 className="font-medium text-gray-900 mb-3">Product Tags</h3>
              <div className="flex flex-wrap gap-2">
                {product.MetaKeywords.split(",").map((keyword, index) => (
                  <Badge key={index} variant="secondary" className="text-xs bg-white/70 hover:bg-white transition-colors">
                    {keyword.trim()}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Meta Description */}
          {product.MetaDescription && (
            <div className="mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200">
              <h3 className="font-medium text-gray-900 mb-3 flex items-center">
                <Award className="h-5 w-5 text-green-600 mr-2" />
                About This Product
              </h3>
              <p className="text-gray-700 leading-relaxed">{product.MetaDescription}</p>
            </div>
          )}

        </div>
      </div>

      {/* Product Tabs */}
      <div className="mt-12">
        <Tabs 
          defaultValue="description" 
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <TabsList className="grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2">
            <TabsTrigger value="description" className="shadow-sm hover:shadow transition-shadow">Description</TabsTrigger>
            <TabsTrigger value="specifications" className="shadow-sm hover:shadow transition-shadow">Specifications</TabsTrigger>
            <TabsTrigger value="reviews" className="shadow-sm hover:shadow transition-shadow">Reviews</TabsTrigger>
            <TabsTrigger value="shipping" className="shadow-sm hover:shadow transition-shadow">Shipping & Returns</TabsTrigger>
          </TabsList>

          <TabsContent value="description" className="mt-4 bg-white rounded-lg shadow-sm">
            <div className="p-8">
              {product.FullDescription ? (
                <div
                  className="prose max-w-none"
                  dangerouslySetInnerHTML={{ __html: product.FullDescription }}
                />
              ) : (
                <p className="text-gray-500 italic">No description available for this product.</p>
              )}
            </div>
          </TabsContent>

          <TabsContent value="specifications" className="mt-4 bg-white rounded-lg shadow-sm">
            <div className="p-8">
              {product.AttributesJson && product.AttributesJson.length > 0 ? (
                <ProductSpecifications
                  attributes={product.AttributesJson}
                  className="bg-white rounded-lg"
                />
              ) : (
                <div className="text-gray-500 italic">No specifications available for this product.</div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="reviews" className="mt-4 bg-white rounded-lg shadow-sm">
            <div className="p-8">
              <div className="space-y-6">
                <div className="flex flex-col sm:flex-row sm:items-center gap-4">
                  <div className="flex items-center">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <Star
                        key={star}
                        className={`w-6 h-6 ${
                          star <= Math.floor(product.Rating || 0)
                            ? "text-yellow-400 fill-yellow-400"
                            : "text-gray-300"
                        }`}
                      />
                    ))}
                  </div>
                  <div className="text-sm text-gray-600">
                    <span className="font-medium">{product.Rating?.toFixed(1) || '0.0'}</span> out of 5
                    {product.TotalReviews ? (
                      <span> • {product.TotalReviews} review{product.TotalReviews !== 1 ? 's' : ''}</span>
                    ) : null}
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium mb-2">Customer Reviews</h4>
                  {product.TotalReviews ? (
                    <div className="space-y-4">
                      {/* Placeholder for reviews list */}
                      <div className="text-center py-8 text-gray-500">
                        Reviews will be displayed here
                      </div>
                    </div>
                  ) : (
                    <div className="text-center py-8">
                      <p className="text-gray-500 mb-4">No reviews yet</p>
                      <Button variant="outline" size="sm">
                        Be the first to review
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="shipping" className="mt-4 bg-white rounded-lg shadow-sm">
            <div className="p-8">
              <div className="space-y-4">
                <p className="text-gray-600">Shipping and delivery information will be provided during checkout based on your location.</p>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <div className="p-4 border rounded-lg">
                    <Truck className="h-6 w-6 text-primary mb-2" />
                    <h4 className="font-medium mb-1">Fast Delivery</h4>
                    <p className="text-sm text-gray-500">Estimated delivery time: {product.EstimatedShippingDays || '3-5'} business days</p>
                  </div>
                  {product.IsReturnAble && (
                    <div className="p-4 border rounded-lg">
                      <RotateCcw className="h-6 w-6 text-primary mb-2" />
                      <h4 className="font-medium mb-1">Easy Returns</h4>
                      <p className="text-sm text-gray-500">Hassle-free returns within 30 days</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>


            </Tabs>
          </div>
      </div>
  )
}

export default function ProductPage() {
  const params = useParams();
  const id = params.id as string;

  if (!id) {
    return <ProductError error="Product ID not found" retry={() => {}} />
  }

  return <ProductDetails productId={id} />
}