import { Metadata } from 'next'
import axios from "axios"
import ProductDetailsClient from './product-details-client'


interface ProductImage {
  AttachmentID: number
  AttachmentName: string
  AttachmentURL: string
  ProductID: number
  IsPrimary?: boolean
}

interface Product {
  ProductId: number
  ProductName: string
  ShortDescription?: string
  FullDescription?: string
  Price: number
  DiscountPrice?: number
  StockQuantity: number
  MetaTitle?: string
  MetaKeywords?: string
  MetaDescription?: string
  CategoryName?: string
  ProductImagesJson: ProductImage[]
}

// Helper function to construct image URL
const constructImageUrl = (attachmentUrl: string | null) => {
  if (!attachmentUrl) return "/placeholder.svg?height=400&width=400"

  if (attachmentUrl.startsWith("http")) {
    return attachmentUrl
  }

  const baseUrl = "https://admin.codemedicalapps.com"
  const normalizedAttachmentUrl = attachmentUrl.startsWith("/") ? attachmentUrl : `/${attachmentUrl}`

  return `${baseUrl}${normalizedAttachmentUrl}`
}

// Helper function to fetch product data for metadata
async function fetchProductForMetadata(productId: string): Promise<Product | null> {
  try {
    const data = JSON.stringify({
      requestParameters: {
        ProductId: Number.parseInt(productId, 10),
        recordValueJson: "[]",
      },
    });

    const config = {
      method: "post",
      maxBodyLength: Number.POSITIVE_INFINITY,
      url: "https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail",
      headers: {
        Accept: "application/json",
        "Content-Type": "application/json",
      },
      data: data,
    };

    const response = await axios.request(config);

    if (response.data && response.data.data) {
      const parsedData = JSON.parse(response.data.data);
      const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;
      return productData || null;
    }
    return null;
  } catch (error) {
    console.error("Error fetching product for metadata:", error);
    return null;
  }
}

// Generate metadata for SEO
export async function generateMetadata({ params }: { params: Promise<{ id: string }> }): Promise<Metadata> {
  const { id } = await params;
  const product = await fetchProductForMetadata(id);

  if (!product) {
    return {
      title: 'Product Not Found - Medical Equipment',
      description: 'The requested product could not be found.',
    };
  }

  const metaTitle = product.MetaTitle || `${product.ProductName} - Medical Equipment`;
  const metaDescription = product.MetaDescription || product.ShortDescription || `Buy ${product.ProductName} at the best price. High-quality medical equipment with fast delivery.`;
  const metaKeywords = product.MetaKeywords || `${product.ProductName}, medical equipment, healthcare, ${product.CategoryName || 'medical supplies'}`;

  const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0
    ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL)
    : '/placeholder.svg?height=400&width=400';

  return {
    title: metaTitle,
    description: metaDescription,
    keywords: metaKeywords,
    openGraph: {
      title: metaTitle,
      description: metaDescription,
      type: 'website',
      images: [
        {
          url: productImage,
          width: 400,
          height: 400,
          alt: product.ProductName,
        },
      ],
    },
    twitter: {
      card: 'summary_large_image',
      title: metaTitle,
      description: metaDescription,
      images: [productImage],
    },
    other: {
      'product:price:amount': (product.DiscountPrice || product.Price).toString(),
      'product:price:currency': 'USD',
      'product:availability': product.StockQuantity > 0 ? 'in stock' : 'out of stock',
      'product:condition': 'new',
    },
  };
}

// Server component that renders the client component
export default async function ProductPage({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params;
  return <ProductDetailsClient productId={id} />
}