"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// SEO Head Component\nfunction ProductSEOHead(param) {\n    let { product } = param;\n    var _product_DiscountPrice;\n    if (!product) return null;\n    const metaTitle = product.MetaTitle || \"\".concat(product.ProductName, \" - Medical Equipment\");\n    const metaDescription = product.MetaDescription || product.ShortDescription || \"Buy \".concat(product.ProductName, \" at the best price. High-quality medical equipment with fast delivery.\");\n    const metaKeywords = product.MetaKeywords || \"\".concat(product.ProductName, \", medical equipment, healthcare, \").concat(product.CategoryName || 'medical supplies');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Head, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: metaKeywords\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: \"product\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:url\",\n                content:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: \"summary_large_image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:amount\",\n                content: ((_product_DiscountPrice = product.DiscountPrice) === null || _product_DiscountPrice === void 0 ? void 0 : _product_DiscountPrice.toString()) || product.Price.toString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:currency\",\n                content: \"USD\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:availability\",\n                content: product.StockQuantity > 0 ? \"in stock\" : \"out of stock\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:condition\",\n                content: \"new\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"canonical\",\n                href:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSEOHead;\n// Helper function to construct image URL (moved outside component for reuse)\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetails(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetails.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetails.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetails.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetails.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetails.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Using the exact configuration provided\n            const data = JSON.stringify({\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            });\n            const config = {\n                method: \"post\",\n                maxBodyLength: Number.POSITIVE_INFINITY,\n                url: \"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                data: data\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_15__[\"default\"].request(config);\n            console.log(\"Product detail API response:\", response.data);\n            if (response.data && response.data.data) {\n                try {\n                    // Parse the response data\n                    const parsedData = JSON.parse(response.data.data);\n                    console.log(\"Parsed product data:\", parsedData);\n                    if (parsedData) {\n                        // The API might return an array with one item or a single object\n                        const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                        if (productData) {\n                            // Ensure AttributesJson is properly parsed if it's a string\n                            if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                try {\n                                    productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                } catch (e) {\n                                    console.error('Error parsing AttributesJson:', e);\n                                    productData.AttributesJson = [];\n                                }\n                            } else if (!productData.AttributesJson) {\n                                productData.AttributesJson = [];\n                            }\n                            console.log('Product data with attributes:', productData);\n                            setProduct(productData);\n                            // Set active image\n                            if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                            }\n                            // Handle comma-separated video links\n                            if (productData.VideoLink) {\n                                console.log(\"Video links found:\", productData.VideoLink);\n                                const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                setVideoLinks(processedLinks);\n                                setSelectedVideoIndex(0);\n                            }\n                            // Set initial quantity based on product minimum order quantity\n                            if (productData.OrderMinimumQuantity > 0) {\n                                setQuantity(productData.OrderMinimumQuantity);\n                            }\n                        } else {\n                            setError(\"No product data found\");\n                        }\n                    } else {\n                        setError(\"Invalid product data format\");\n                    }\n                } catch (parseError) {\n                    setError(\"Error parsing product data\");\n                    console.error(\"Error parsing product data:\", parseError);\n                }\n            } else {\n                setError(\"No data in API response\");\n            }\n        } catch (error) {\n            setError(\"Error fetching product details\");\n            console.error(\"Error fetching product:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetails.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetails.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetails.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetails.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetails.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetails.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetails.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetails.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetails.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetails.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 539,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 561,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 565,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 570,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSEOHead, {\n                product: product\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.Breadcrumb, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbList, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products\",\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 11\n                                }, this),\n                                product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/products/category/\".concat(product.CategoryID),\n                                                    children: product.CategoryName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbPage, {\n                                        children: product.ProductName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:w-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_12__.ProductMediaGallery, {\n                                    media: mediaItems,\n                                    className: \"w-full rounded-lg overflow-hidden\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold mb-2\",\n                                        children: product.ProductName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 17\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 ml-2\",\n                                                children: [\n                                                    \"(\",\n                                                    product.Rating || 0,\n                                                    \") \",\n                                                    product.TotalReviews || 0,\n                                                    \" reviews\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 11\n                                    }, this),\n                                    renderPrice(),\n                                    product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-sm max-w-none mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: product.ShortDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 flex items-center\",\n                                        children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"In Stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.StockQuantity,\n                                                        \" available)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-red-600\",\n                                            children: \"Out of Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 border-t border-gray-200 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: \"Product Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-4\",\n                                                children: \"Choose your preferences from the options below.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 13\n                                            }, this),\n                                            Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: Object.entries(groupedAttributes).map((param)=>{\n                                                    let [groupId, attributes] = param;\n                                                    var _attributes_, _attributes_1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: [\n                                                                    ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pl-4\",\n                                                                children: attributes.map((attr)=>{\n                                                                    const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                                    const isSelected = !!selectedAttributes[attrKey];\n                                                                    const isRadioGroup = attributes.length > 1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center h-5\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                                    id: \"attr-\".concat(attrKey),\n                                                                                    name: \"attr-group-\".concat(groupId),\n                                                                                    className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                                    checked: isSelected,\n                                                                                    onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 699,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 text-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"attr-\".concat(attrKey),\n                                                                                    className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                                    children: [\n                                                                                        attr.AttributeValueText,\n                                                                                        (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                            children: [\n                                                                                                \"(\",\n                                                                                                attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                                \"$\",\n                                                                                                attr.PriceAdjustment,\n                                                                                                \" \",\n                                                                                                attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, attrKey, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, \"attr-group-\".concat(groupId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"No additional product details available.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-4 text-sm font-medium\",\n                                                        children: \"Quantity:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: decrementQuantity,\n                                                                className: getButtonStyles('decrement'),\n                                                                disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                                \"aria-label\": \"Decrease quantity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: incrementQuantity,\n                                                                className: getButtonStyles('increment'),\n                                                                disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                                \"aria-label\": \"Increase quantity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Min: \",\n                                                            product.OrderMinimumQuantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 13\n                                            }, this),\n                                            product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                        backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: product.StockQuantity <= 0 || addingToCart,\n                                                onClick: ()=>{\n                                                    if (!product) return;\n                                                    setAddingToCart(true);\n                                                    try {\n                                                        // Get the first product image or use a placeholder\n                                                        const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                        // Get selected attributes\n                                                        const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                        // Add to cart using the cart context with attributes and adjusted price\n                                                        cart.addToCart({\n                                                            id: product.ProductId,\n                                                            name: product.ProductName,\n                                                            price: product.DiscountPrice || product.Price,\n                                                            discountPrice: product.DiscountPrice,\n                                                            image: productImage,\n                                                            originalPrice: product.Price\n                                                        }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                        );\n                                                        // Show success toast\n                                                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                                    } catch (error) {\n                                                        console.error('Error adding to cart:', error);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                                    } finally{\n                                                        setAddingToCart(false);\n                                                    }\n                                                },\n                                                children: [\n                                                    addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: addingToWishlist,\n                                                onClick: ()=>{\n                                                    if (!product) return;\n                                                    setAddingToWishlist(true);\n                                                    try {\n                                                        // Check if product is already in wishlist\n                                                        const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                        if (isAlreadyInWishlist) {\n                                                            // Remove from wishlist if already there\n                                                            wishlist.removeFromWishlist(product.ProductId);\n                                                            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                        } else {\n                                                            // Add to wishlist\n                                                            wishlist.addToWishlist(product.ProductId);\n                                                            sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                        }\n                                                    } catch (error) {\n                                                        console.error('Error updating wishlist:', error);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error('Failed to update wishlist. Please try again.');\n                                                    } finally{\n                                                        setAddingToWishlist(false);\n                                                    }\n                                                },\n                                                children: [\n                                                    addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\",\n                                                        fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only md:not-sr-only md:inline\",\n                                                        children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                                onClick: ()=>{\n                                                    if (navigator.share) {\n                                                        navigator.share({\n                                                            title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                            text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                            url: window.location.href\n                                                        }).catch((err)=>console.error(\"Error sharing:\", err));\n                                                    } else {\n                                                        // Fallback - copy to clipboard\n                                                        navigator.clipboard.writeText(window.location.href);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Product link copied to clipboard\");\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only md:not-sr-only md:inline\",\n                                                        children: \"Share\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 11\n                                    }, this),\n                                    product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900 mb-3\",\n                                                children: \"Product Tags\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 921,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-2\",\n                                                children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                                        variant: \"secondary\",\n                                                        className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                        children: keyword.trim()\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 924,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5 text-green-600 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 936,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"About This Product\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 935,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-700 leading-relaxed\",\n                                                children: product.MetaDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                            defaultValue: \"description\",\n                            className: \"w-full\",\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                                    className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"description\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 955,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"specifications\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Specifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 956,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"reviews\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Reviews\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 957,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                            value: \"shipping\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Shipping & Returns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 958,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 954,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"description\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"prose max-w-none\",\n                                            dangerouslySetInnerHTML: {\n                                                __html: product.FullDescription\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 964,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-500 italic\",\n                                            children: \"No description available for this product.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 969,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 961,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"specifications\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_11__.ProductSpecifications, {\n                                            attributes: product.AttributesJson,\n                                            className: \"bg-white rounded-lg\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 977,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-gray-500 italic\",\n                                            children: \"No specifications available for this product.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 982,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 975,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 974,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"reviews\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex items-center\",\n                                                            children: [\n                                                                1,\n                                                                2,\n                                                                3,\n                                                                4,\n                                                                5\n                                                            ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                    className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                                }, star, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 993,\n                                                                    columnNumber: 23\n                                                                }, this))\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 991,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-sm text-gray-600\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: \"font-medium\",\n                                                                    children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1004,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                \" out of 5\",\n                                                                product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    children: [\n                                                                        \" • \",\n                                                                        product.TotalReviews,\n                                                                        \" review\",\n                                                                        product.TotalReviews !== 1 ? 's' : ''\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1006,\n                                                                    columnNumber: 23\n                                                                }, this) : null\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1003,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 990,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-gray-50 p-4 rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-2\",\n                                                            children: \"Customer Reviews\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1012,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"space-y-4\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-center py-8 text-gray-500\",\n                                                                children: \"Reviews will be displayed here\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1016,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1014,\n                                                            columnNumber: 21\n                                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-500 mb-4\",\n                                                                    children: \"No reviews yet\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1022,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                                    variant: \"outline\",\n                                                                    size: \"sm\",\n                                                                    children: \"Be the first to review\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1023,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1021,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1011,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 989,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 988,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 987,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                                    value: \"shipping\",\n                                    className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-8\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"space-y-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-gray-600\",\n                                                    children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1036,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-primary mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1039,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: \"Fast Delivery\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1040,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: [\n                                                                        \"Estimated delivery time: \",\n                                                                        product.EstimatedShippingDays || '3-5',\n                                                                        \" business days\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1041,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1038,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 border rounded-lg\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                    className: \"h-6 w-6 text-primary mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1045,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium mb-1\",\n                                                                    children: \"Easy Returns\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1046,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-500\",\n                                                                    children: \"Hassle-free returns within 30 days\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1047,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1044,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1037,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1035,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1034,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1033,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 948,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 947,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductDetails, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist\n    ];\n});\n_c1 = ProductDetails;\nfunction ProductPage() {\n    _s1();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = params.id;\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            error: \"Product ID not found\",\n            retry: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 1071,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductDetails, {\n        productId: id\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 1074,\n        columnNumber: 10\n    }, this);\n}\n_s1(ProductPage, \"+jVsTcECDRo3yq2d7EQxlN9Ixog=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c2 = ProductPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductSEOHead\");\n$RefreshReg$(_c1, \"ProductDetails\");\n$RefreshReg$(_c2, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});