"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// SEO Head Component\nfunction ProductSEOHead(param) {\n    let { product } = param;\n    var _product_DiscountPrice;\n    if (!product) return null;\n    const metaTitle = product.MetaTitle || \"\".concat(product.ProductName, \" - Medical Equipment\");\n    const metaDescription = product.MetaDescription || product.ShortDescription || \"Buy \".concat(product.ProductName, \" at the best price. High-quality medical equipment with fast delivery.\");\n    const metaKeywords = product.MetaKeywords || \"\".concat(product.ProductName, \", medical equipment, healthcare, \").concat(product.CategoryName || 'medical supplies');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: metaKeywords\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: \"product\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:url\",\n                content:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: \"summary_large_image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:amount\",\n                content: ((_product_DiscountPrice = product.DiscountPrice) === null || _product_DiscountPrice === void 0 ? void 0 : _product_DiscountPrice.toString()) || product.Price.toString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:currency\",\n                content: \"USD\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:availability\",\n                content: product.StockQuantity > 0 ? \"in stock\" : \"out of stock\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:condition\",\n                content: \"new\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"canonical\",\n                href:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSEOHead;\n// Helper function to construct image URL (moved outside component for reuse)\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetails(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetails.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetails.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetails.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetails.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetails.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Using the exact configuration provided\n            const data = JSON.stringify({\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            });\n            const config = {\n                method: \"post\",\n                maxBodyLength: Number.POSITIVE_INFINITY,\n                url: \"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                data: data\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_16__[\"default\"].request(config);\n            console.log(\"Product detail API response:\", response.data);\n            if (response.data && response.data.data) {\n                try {\n                    // Parse the response data\n                    const parsedData = JSON.parse(response.data.data);\n                    console.log(\"Parsed product data:\", parsedData);\n                    if (parsedData) {\n                        // The API might return an array with one item or a single object\n                        const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                        if (productData) {\n                            // Ensure AttributesJson is properly parsed if it's a string\n                            if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                try {\n                                    productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                } catch (e) {\n                                    console.error('Error parsing AttributesJson:', e);\n                                    productData.AttributesJson = [];\n                                }\n                            } else if (!productData.AttributesJson) {\n                                productData.AttributesJson = [];\n                            }\n                            console.log('Product data with attributes:', productData);\n                            setProduct(productData);\n                            // Set active image\n                            if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                            }\n                            // Handle comma-separated video links\n                            if (productData.VideoLink) {\n                                console.log(\"Video links found:\", productData.VideoLink);\n                                const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                setVideoLinks(processedLinks);\n                                setSelectedVideoIndex(0);\n                            }\n                            // Set initial quantity based on product minimum order quantity\n                            if (productData.OrderMinimumQuantity > 0) {\n                                setQuantity(productData.OrderMinimumQuantity);\n                            }\n                        } else {\n                            setError(\"No product data found\");\n                        }\n                    } else {\n                        setError(\"Invalid product data format\");\n                    }\n                } catch (parseError) {\n                    setError(\"Error parsing product data\");\n                    console.error(\"Error parsing product data:\", parseError);\n                }\n            } else {\n                setError(\"No data in API response\");\n            }\n        } catch (error) {\n            setError(\"Error fetching product details\");\n            console.error(\"Error fetching product:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetails.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetails.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetails.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 357,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 364,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 371,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 350,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 379,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 387,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 386,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 396,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 395,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetails.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetails.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 445,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 450,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 443,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetails.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetails.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetails.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetails.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetails.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 540,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 548,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 539,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 561,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 565,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 575,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 574,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 573,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 570,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductSEOHead, {\n                product: product\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/\",\n                                            children: \"Home\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 592,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 591,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 590,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products\",\n                                            children: \"Products\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 598,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 11\n                                }, this),\n                                product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                                    href: \"/products/category/\".concat(product.CategoryID),\n                                                    children: product.CategoryName\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 605,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 609,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbPage, {\n                                        children: product.ProductName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 613,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 612,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 589,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col lg:flex-row gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:w-1/2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__.ProductMediaGallery, {\n                                    media: mediaItems,\n                                    className: \"w-full rounded-lg overflow-hidden\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 621,\n                                    columnNumber: 11\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 620,\n                                columnNumber: 9\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"md:w-1/2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-xl font-bold mb-2\",\n                                        children: product.ProductName\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 629,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex\",\n                                                children: [\n                                                    ...Array(5)\n                                                ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                    }, i, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 637,\n                                                        columnNumber: 17\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 635,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-sm text-gray-500 ml-2\",\n                                                children: [\n                                                    \"(\",\n                                                    product.Rating || 0,\n                                                    \") \",\n                                                    product.TotalReviews || 0,\n                                                    \" reviews\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 647,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 634,\n                                        columnNumber: 11\n                                    }, this),\n                                    renderPrice(),\n                                    product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose prose-sm max-w-none mb-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            children: product.ShortDescription\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 658,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 657,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4 flex items-center\",\n                                        children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center text-green-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                            className: \"h-4 w-4 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 667,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-sm font-medium\",\n                                                            children: \"In Stock\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 668,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 666,\n                                                    columnNumber: 17\n                                                }, this),\n                                                product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm text-gray-500 ml-2\",\n                                                    children: [\n                                                        \"(\",\n                                                        product.StockQuantity,\n                                                        \" available)\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 671,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm font-medium text-red-600\",\n                                            children: \"Out of Stock\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 663,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6 border-t border-gray-200 pt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                                children: \"Product Details\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600 mb-4\",\n                                                children: \"Choose your preferences from the options below.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 682,\n                                                columnNumber: 13\n                                            }, this),\n                                            Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-6\",\n                                                children: Object.entries(groupedAttributes).map((param)=>{\n                                                    let [groupId, attributes] = param;\n                                                    var _attributes_, _attributes_1;\n                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"text-sm font-medium text-gray-700\",\n                                                                children: [\n                                                                    ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                                    \":\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 687,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"space-y-2 pl-4\",\n                                                                children: attributes.map((attr)=>{\n                                                                    const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                                    const isSelected = !!selectedAttributes[attrKey];\n                                                                    const isRadioGroup = attributes.length > 1;\n                                                                    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-start\",\n                                                                        children: [\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"flex items-center h-5\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                                    type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                                    id: \"attr-\".concat(attrKey),\n                                                                                    name: \"attr-group-\".concat(groupId),\n                                                                                    className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                                    checked: isSelected,\n                                                                                    onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 699,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 698,\n                                                                                columnNumber: 29\n                                                                            }, this),\n                                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                                className: \"ml-3 text-sm\",\n                                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                                    htmlFor: \"attr-\".concat(attrKey),\n                                                                                    className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                                    children: [\n                                                                                        attr.AttributeValueText,\n                                                                                        (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                            className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                            children: [\n                                                                                                \"(\",\n                                                                                                attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                                \"$\",\n                                                                                                attr.PriceAdjustment,\n                                                                                                \" \",\n                                                                                                attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                                \")\"\n                                                                                            ]\n                                                                                        }, void 0, true, {\n                                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                            lineNumber: 715,\n                                                                                            columnNumber: 35\n                                                                                        }, this)\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 709,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            }, void 0, false, {\n                                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                lineNumber: 708,\n                                                                                columnNumber: 29\n                                                                            }, this)\n                                                                        ]\n                                                                    }, attrKey, true, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 697,\n                                                                        columnNumber: 27\n                                                                    }, this);\n                                                                })\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 690,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, \"attr-group-\".concat(groupId), true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 686,\n                                                        columnNumber: 19\n                                                    }, this);\n                                                })\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 684,\n                                                columnNumber: 15\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-500\",\n                                                children: \"No additional product details available.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 729,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 680,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"mr-4 text-sm font-medium\",\n                                                        children: \"Quantity:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 736,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: decrementQuantity,\n                                                                className: getButtonStyles('decrement'),\n                                                                disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                                \"aria-label\": \"Decrease quantity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 745,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 744,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 738,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 749,\n                                                                columnNumber: 17\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                                onClick: incrementQuantity,\n                                                                className: getButtonStyles('increment'),\n                                                                disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                                \"aria-label\": \"Increase quantity\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                                    className: \"h-5 w-5\",\n                                                                    viewBox: \"0 0 20 20\",\n                                                                    fill: \"currentColor\",\n                                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                        fillRule: \"evenodd\",\n                                                                        d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                        clipRule: \"evenodd\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 762,\n                                                                        columnNumber: 21\n                                                                    }, this)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 761,\n                                                                    columnNumber: 19\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 751,\n                                                                columnNumber: 17\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 737,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Min: \",\n                                                            product.OrderMinimumQuantity\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 768,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"ml-3 text-xs text-gray-500\",\n                                                        children: [\n                                                            \"Max: \",\n                                                            Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 774,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 735,\n                                                columnNumber: 13\n                                            }, this),\n                                            product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                                    style: {\n                                                        width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                        backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                                    }\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 783,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 782,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 734,\n                                        columnNumber: 11\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: product.StockQuantity <= 0 || addingToCart,\n                                                onClick: ()=>{\n                                                    if (!product) return;\n                                                    setAddingToCart(true);\n                                                    try {\n                                                        // Get the first product image or use a placeholder\n                                                        const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                        // Get selected attributes\n                                                        const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                        // Add to cart using the cart context with attributes and adjusted price\n                                                        cart.addToCart({\n                                                            id: product.ProductId,\n                                                            name: product.ProductName,\n                                                            price: product.DiscountPrice || product.Price,\n                                                            discountPrice: product.DiscountPrice,\n                                                            image: productImage,\n                                                            originalPrice: product.Price\n                                                        }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                        );\n                                                        // Show success toast\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                                    } catch (error) {\n                                                        console.error('Error adding to cart:', error);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                                    } finally{\n                                                        setAddingToCart(false);\n                                                    }\n                                                },\n                                                children: [\n                                                    addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 842,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 844,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 846,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 797,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                                disabled: addingToWishlist,\n                                                onClick: ()=>{\n                                                    if (!product) return;\n                                                    setAddingToWishlist(true);\n                                                    try {\n                                                        // Check if product is already in wishlist\n                                                        const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                        if (isAlreadyInWishlist) {\n                                                            // Remove from wishlist if already there\n                                                            wishlist.removeFromWishlist(product.ProductId);\n                                                            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                        } else {\n                                                            // Add to wishlist\n                                                            wishlist.addToWishlist(product.ProductId);\n                                                            sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                        }\n                                                    } catch (error) {\n                                                        console.error('Error updating wishlist:', error);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error('Failed to update wishlist. Please try again.');\n                                                    } finally{\n                                                        setAddingToWishlist(false);\n                                                    }\n                                                },\n                                                children: [\n                                                    addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 880,\n                                                        columnNumber: 17\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-5 w-5\",\n                                                        fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 882,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only md:not-sr-only md:inline\",\n                                                        children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 887,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 850,\n                                                columnNumber: 13\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                                onClick: ()=>{\n                                                    if (navigator.share) {\n                                                        navigator.share({\n                                                            title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                            text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                            url: window.location.href\n                                                        }).catch((err)=>console.error(\"Error sharing:\", err));\n                                                    } else {\n                                                        // Fallback - copy to clipboard\n                                                        navigator.clipboard.writeText(window.location.href);\n                                                        sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Product link copied to clipboard\");\n                                                    }\n                                                },\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-5 w-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 913,\n                                                        columnNumber: 15\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"sr-only md:not-sr-only md:inline\",\n                                                        children: \"Share\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 914,\n                                                        columnNumber: 15\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 894,\n                                                columnNumber: 13\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 795,\n                                        columnNumber: 11\n                                    }, this),\n                                    product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-6 flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 920,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-12 p-4 bg-gray-50 rounded-lg border\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"About This Product\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: product.MetaDescription\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 932,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 628,\n                                columnNumber: 9\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 618,\n                        columnNumber: 7\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                            defaultValue: \"description\",\n                            className: \"w-full\",\n                            value: activeTab,\n                            onValueChange: setActiveTab,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                                    className: \"grid w-full grid-cols-2 sm:grid-cols-5 mb-6 gap-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"description\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Description\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 948,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"specifications\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Specifications\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 949,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"reviews\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Reviews\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 950,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"shipping\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Shipping & Returns\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 951,\n                                            columnNumber: 13\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                            value: \"seo-info\",\n                                            className: \"shadow-sm hover:shadow transition-shadow\",\n                                            children: \"Product Info\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 952,\n                                            columnNumber: 13\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 947,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"description\",\n                                    className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                                    children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"prose max-w-none\",\n                                        dangerouslySetInnerHTML: {\n                                            __html: product.FullDescription\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 957,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-500 italic\",\n                                        children: \"No description available for this product.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 962,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 955,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"specifications\",\n                                    className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                                    children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__.ProductSpecifications, {\n                                        attributes: product.AttributesJson,\n                                        className: \"bg-white rounded-lg\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 968,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-gray-500 italic\",\n                                        children: \"No specifications available for this product.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 973,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 966,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"reviews\",\n                                    className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            1,\n                                                            2,\n                                                            3,\n                                                            4,\n                                                            5\n                                                        ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                            }, star, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 982,\n                                                                columnNumber: 21\n                                                            }, this))\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 980,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-sm text-gray-600\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 993,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            \" out of 5\",\n                                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    \" • \",\n                                                                    product.TotalReviews,\n                                                                    \" review\",\n                                                                    product.TotalReviews !== 1 ? 's' : ''\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 995,\n                                                                columnNumber: 21\n                                                            }, this) : null\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 979,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-50 p-4 rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-2\",\n                                                        children: \"Customer Reviews\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"text-center py-8 text-gray-500\",\n                                                            children: \"Reviews will be displayed here\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1005,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 19\n                                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-gray-500 mb-4\",\n                                                                children: \"No reviews yet\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1011,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                                variant: \"outline\",\n                                                                size: \"sm\",\n                                                                children: \"Be the first to review\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1012,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1010,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 978,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"shipping\",\n                                    className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600\",\n                                                children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1023,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-6 w-6 text-primary mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1026,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Fast Delivery\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1027,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: [\n                                                                    \"Estimated delivery time: \",\n                                                                    product.EstimatedShippingDays || '3-5',\n                                                                    \" business days\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1028,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1025,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-4 border rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                className: \"h-6 w-6 text-primary mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1032,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                className: \"font-medium mb-1\",\n                                                                children: \"Easy Returns\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1033,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-500\",\n                                                                children: \"Hassle-free returns within 30 days\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 1034,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1031,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1024,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1022,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1021,\n                                    columnNumber: 11\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                                    value: \"seo-info\",\n                                    className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-lg font-semibold text-gray-900 mb-4\",\n                                                    children: \"Product Information\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1044,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: [\n                                                        product.MetaTitle && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-blue-50 rounded-lg border border-blue-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-blue-900 mb-2\",\n                                                                    children: \"SEO Title\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1048,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-blue-800\",\n                                                                    children: product.MetaTitle\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1049,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1047,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-green-50 rounded-lg border border-green-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-green-900 mb-2\",\n                                                                    children: \"Product Description\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1055,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-green-800\",\n                                                                    children: product.MetaDescription\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1056,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1054,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-purple-50 rounded-lg border border-purple-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-purple-900 mb-3\",\n                                                                    children: \"Related Keywords\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1062,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex flex-wrap gap-2\",\n                                                                    children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                            variant: \"secondary\",\n                                                                            className: \"bg-purple-100 text-purple-800 hover:bg-purple-200\",\n                                                                            children: keyword.trim()\n                                                                        }, index, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 1065,\n                                                                            columnNumber: 27\n                                                                        }, this))\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1063,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1061,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-orange-50 rounded-lg border border-orange-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-orange-900 mb-2\",\n                                                                    children: \"Category\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1075,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                                                    className: \"bg-orange-100 text-orange-800 hover:bg-orange-200\",\n                                                                    children: product.CategoryName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1076,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1074,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        product.VendorName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"p-4 bg-gray-50 rounded-lg border border-gray-200\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                    className: \"font-medium text-gray-900 mb-2\",\n                                                                    children: \"Vendor\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1084,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-gray-700\",\n                                                                    children: product.VendorName\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                    lineNumber: 1085,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 1083,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1045,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 1043,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1042,\n                                        columnNumber: 13\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 1041,\n                                    columnNumber: 11\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 941,\n                            columnNumber: 9\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 940,\n                        columnNumber: 7\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 586,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(ProductDetails, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist\n    ];\n});\n_c1 = ProductDetails;\nfunction ProductPage() {\n    _s1();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = params.id;\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: \"Product ID not found\",\n            retry: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 1107,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductDetails, {\n        productId: id\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 1110,\n        columnNumber: 10\n    }, this);\n}\n_s1(ProductPage, \"+jVsTcECDRo3yq2d7EQxlN9Ixog=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c2 = ProductPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductSEOHead\");\n$RefreshReg$(_c1, \"ProductDetails\");\n$RefreshReg$(_c2, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});