"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Try direct API call first\n                response = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(\"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Direct API response:\", response.data);\n            } catch (directError) {\n                console.log(\"Direct API failed, trying proxy route:\", directError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error('Error parsing AttributesJson:', e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log('Product data with attributes:', productData);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product with ID \".concat(productId, \" not found. Please check if this product exists.\"));\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Unknown error'));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 538,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 560,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 564,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 569,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this),\n                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/products/category/\".concat(product.CategoryID),\n                                            children: product.CategoryName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_11__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex items-center\",\n                                children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"In Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"(\",\n                                                product.StockQuantity,\n                                                \" available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-red-600\",\n                                    children: \"Out of Stock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-4 text-sm font-medium\",\n                                                children: \"Quantity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: decrementQuantity,\n                                                        className: getButtonStyles('decrement'),\n                                                        disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                        \"aria-label\": \"Decrease quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: incrementQuantity,\n                                                        className: getButtonStyles('increment'),\n                                                        disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                        \"aria-label\": \"Increase quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Min: \",\n                                                    product.OrderMinimumQuantity\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Max: \",\n                                                    Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                );\n                                                // Show success toast\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                            } catch (error) {\n                                                console.error('Error adding to cart:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    // Add to wishlist\n                                                    wishlist.addToWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error('Error updating wishlist:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to update wishlist. Please try again.');\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 615,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"specifications\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Shipping & Returns\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 949,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: product.FullDescription\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 italic\",\n                                    children: \"No description available for this product.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"specifications\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_10__.ProductSpecifications, {\n                                    attributes: product.AttributesJson,\n                                    className: \"bg-white rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 italic\",\n                                    children: \"No specifications available for this product.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 969,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                        }, star, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 999,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" out of 5\",\n                                                        product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \" • \",\n                                                                product.TotalReviews,\n                                                                \" review\",\n                                                                product.TotalReviews !== 1 ? 's' : ''\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 23\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Customer Reviews\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: \"Reviews will be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1011,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No reviews yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1017,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: \"Be the first to review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 983,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-6 w-6 text-primary mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1034,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Fast Delivery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1035,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Estimated delivery time: \",\n                                                                product.EstimatedShippingDays || '3-5',\n                                                                \" business days\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1036,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1033,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-6 w-6 text-primary mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1040,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Easy Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Hassle-free returns within 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1029,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1028,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 943,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 942,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 583,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__.useWishlist\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ })

});