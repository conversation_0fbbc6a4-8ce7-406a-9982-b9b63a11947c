"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProductDetails(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetails.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetails.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetails.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetails.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetails.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Using the exact configuration provided\n            const data = JSON.stringify({\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            });\n            const config = {\n                method: \"post\",\n                maxBodyLength: Number.POSITIVE_INFINITY,\n                url: \"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                data: data\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_15__[\"default\"].request(config);\n            console.log(\"Product detail API response:\", response.data);\n            if (response.data && response.data.data) {\n                try {\n                    // Parse the response data\n                    const parsedData = JSON.parse(response.data.data);\n                    console.log(\"Parsed product data:\", parsedData);\n                    if (parsedData) {\n                        // The API might return an array with one item or a single object\n                        const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                        if (productData) {\n                            // Ensure AttributesJson is properly parsed if it's a string\n                            if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                try {\n                                    productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                } catch (e) {\n                                    console.error('Error parsing AttributesJson:', e);\n                                    productData.AttributesJson = [];\n                                }\n                            } else if (!productData.AttributesJson) {\n                                productData.AttributesJson = [];\n                            }\n                            console.log('Product data with attributes:', productData);\n                            setProduct(productData);\n                            // Set active image\n                            if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                            }\n                            // Handle comma-separated video links\n                            if (productData.VideoLink) {\n                                console.log(\"Video links found:\", productData.VideoLink);\n                                const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                setVideoLinks(processedLinks);\n                                setSelectedVideoIndex(0);\n                            }\n                            // Set initial quantity based on product minimum order quantity\n                            if (productData.OrderMinimumQuantity > 0) {\n                                setQuantity(productData.OrderMinimumQuantity);\n                            }\n                        } else {\n                            setError(\"No product data found\");\n                        }\n                    } else {\n                        setError(\"Invalid product data format\");\n                    }\n                } catch (parseError) {\n                    setError(\"Error parsing product data\");\n                    console.error(\"Error parsing product data:\", parseError);\n                }\n            } else {\n                setError(\"No data in API response\");\n            }\n        } catch (error) {\n            setError(\"Error fetching product details\");\n            console.error(\"Error fetching product:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructImageUrl = (attachmentUrl)=>{\n        if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n        if (attachmentUrl.startsWith(\"http\")) {\n            return attachmentUrl;\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n        return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetails.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetails.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetails.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 313,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 319,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 326,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 333,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 312,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 348,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 359,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 357,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 310,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetails.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetails.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 407,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 412,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 405,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetails.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetails.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetails.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetails.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetails.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 502,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 510,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 501,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 523,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 527,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 533,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 534,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 536,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 535,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 532,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 552,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 551,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 550,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 555,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 558,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 557,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 556,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 561,\n                            columnNumber: 11\n                        }, this),\n                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products/category/\".concat(product.CategoryID),\n                                            children: product.CategoryName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 566,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 565,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 564,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_5__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 572,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 549,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 548,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_12__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 581,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 580,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 597,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 595,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 607,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 617,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex items-center\",\n                                children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"In Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 628,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 626,\n                                            columnNumber: 17\n                                        }, this),\n                                        product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"(\",\n                                                product.StockQuantity,\n                                                \" available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 631,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-red-600\",\n                                    children: \"Out of Stock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 635,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 623,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 641,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 647,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 659,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 658,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 675,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 669,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 668,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 657,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 650,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 689,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 640,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-4 text-sm font-medium\",\n                                                children: \"Quantity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 696,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: decrementQuantity,\n                                                        className: getButtonStyles('decrement'),\n                                                        disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                        \"aria-label\": \"Decrease quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 705,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 704,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 698,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 709,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: incrementQuantity,\n                                                        className: getButtonStyles('increment'),\n                                                        disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                        \"aria-label\": \"Increase quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 722,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 721,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 711,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 697,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Min: \",\n                                                    product.OrderMinimumQuantity\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 728,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Max: \",\n                                                    Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 734,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 695,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 743,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 742,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 694,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                );\n                                                // Show success toast\n                                                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                            } catch (error) {\n                                                console.error('Error adding to cart:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 802,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 804,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 806,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 757,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    // Add to wishlist\n                                                    wishlist.addToWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error('Error updating wishlist:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.error('Failed to update wishlist. Please try again.');\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 840,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 842,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 847,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 810,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_8__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 873,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 874,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 854,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 755,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-wrap gap-2\",\n                                children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_7__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs\",\n                                        children: keyword.trim()\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 882,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 880,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 p-4 bg-gray-50 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"About This Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 891,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 892,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 890,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 588,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 578,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsList, {\n                            className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 908,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"specifications\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 909,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 910,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Shipping & Returns\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 911,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 907,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose max-w-none\",\n                                dangerouslySetInnerHTML: {\n                                    __html: product.FullDescription\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 916,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No description available for this product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 921,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 914,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"specifications\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_11__.ProductSpecifications, {\n                                attributes: product.AttributesJson,\n                                className: \"bg-white rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 927,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No specifications available for this product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 932,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 925,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5\n                                                ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                    }, star, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 941,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 939,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 952,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" out of 5\",\n                                                    product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \" • \",\n                                                            product.TotalReviews,\n                                                            \" review\",\n                                                            product.TotalReviews !== 1 ? 's' : ''\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 954,\n                                                        columnNumber: 21\n                                                    }, this) : null\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 951,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 938,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Customer Reviews\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 960,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-gray-500\",\n                                                    children: \"Reviews will be displayed here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 964,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 962,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 mb-4\",\n                                                        children: \"No reviews yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 970,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Be the first to review\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 971,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 969,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 959,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 937,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 936,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_6__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 982,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-6 w-6 text-primary mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 985,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"Fast Delivery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 986,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Estimated delivery time: \",\n                                                            product.EstimatedShippingDays || '3-5',\n                                                            \" business days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 987,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 984,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-primary mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 991,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"Easy Returns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 992,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Hassle-free returns within 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 993,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 990,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 983,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 980,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 901,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 900,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 546,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetails, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_9__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_10__.useWishlist\n    ];\n});\n_c = ProductDetails;\nfunction ProductPage() {\n    _s1();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = params.id;\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n            error: \"Product ID not found\",\n            retry: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 1013,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductDetails, {\n        productId: id\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 1016,\n        columnNumber: 10\n    }, this);\n}\n_s1(ProductPage, \"+jVsTcECDRo3yq2d7EQxlN9Ixog=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c1 = ProductPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"ProductDetails\");\n$RefreshReg$(_c1, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ })

});