using DAL.DBContext;
using DAL.Repository.IServices;
using Entities.DBInheritedModels;
using Entities.DBModels;
using Helpers.ApiHelpers;
using Helpers.AuthorizationHelpers;
using Helpers.AuthorizationHelpers.JwtTokenHelper;
using Helpers.CommonHelpers;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System.Data;

namespace AdminPanel.Areas.V1.Controllers
{
    [Route("api/v1/products")]
    [ApiController]
    [Area("V1")]
    public class ApiProductsController : ApiBaseController
    {
        private readonly ICommonServicesDAL _commonServicesDAL;
        private readonly ISessionManager _sessionManag;
        private readonly IConstants _constants;
        private readonly IProductServicesDAL _productServicesDAL;
        private readonly IBasicDataServicesDAL _basicDataDAL;
        private readonly IDataContextHelper _dataContextHelper;

        public ApiProductsController(
            ICommonServicesDAL commonServicesDAL,
            ISessionManager sessionManag,
            IConstants constants,
            IProductServicesDAL productServicesDAL,
            IBasicDataServicesDAL basicDataDAL,
            IDataContextHelper dataContextHelper)
        {
            _commonServicesDAL = commonServicesDAL;
            _sessionManag = sessionManag;
            _constants = constants;
            _productServicesDAL = productServicesDAL;
            _basicDataDAL = basicDataDAL;
            _dataContextHelper = dataContextHelper;
        }

        [HttpPost]
        [Route("get-all-products")]
        [ServiceFilter(typeof(CustomerApiCallsAuthorization))]
        public async Task<APIActionResult> GetAllProducts([FromBody] Dictionary<string, object> param)
        {
            #region Basic declaration
            string resultType = "json";
            CodeMedicalAppAPIResult result = new CodeMedicalAppAPIResult();
            APIActionResult apiActionResult;
            result.ActionType = (ActionTypeEnum)Enum.Parse(typeof(ActionTypeEnum), resultType, true);
            string? data = string.Empty;
            #endregion

            try
            {
                Dictionary<string, object>? requestParameters = new Dictionary<string, object>();
                if (param.ContainsKey("requestParameters"))
                {
                    string? paramKeyValue = param["requestParameters"].ToString();
                    if (!String.IsNullOrWhiteSpace(paramKeyValue))
                    {
                        requestParameters = JsonConvert.DeserializeObject<Dictionary<string, object>>(paramKeyValue);
                    }
                }

                // Extract parameters
                string searchTerm = GetParameterValue<string>(requestParameters, "SearchTerm", "");
                string categoryId = GetParameterValue<string>(requestParameters, "CategoryID", "");
                string tagId = GetParameterValue<string>(requestParameters, "TagID", "");
                string manufacturerId = GetParameterValue<string>(requestParameters, "ManufacturerID", "");
                int producttypeId = GetParameterValue<int>(requestParameters, "producttypeId", 0);
                int minPrice = GetParameterValue<int>(requestParameters, "MinPrice", 0);
                int maxPrice = GetParameterValue<int>(requestParameters, "MaxPrice", 0);
                int rating = GetParameterValue<int>(requestParameters, "Rating", 0);
                string orderByColumnName = GetParameterValue<string>(requestParameters, "OrderByColumnName", "");
                int pageNo = GetParameterValue<int>(requestParameters, "PageNo", 1);
                int pageSize = GetParameterValue<int>(requestParameters, "PageSize", 20);

                try
                {
                    // Create a ProductEntity for the API call
                    ProductEntity productEntity = new ProductEntity
                    {
                        ProductName = searchTerm,
                        PageNo = pageNo,
                        PageSize = pageSize
                    };

                    // Try to parse categoryId as integer if it's not empty
                    if (!string.IsNullOrEmpty(categoryId) && int.TryParse(categoryId, out int catId))
                    {
                        productEntity.CategoryId = catId;
                    }

                    // Use the ProductServicesDAL to get the product list
                    var productList = await _productServicesDAL.GetProductList(productEntity);

                    if (productList != null && productList.Count > 0)
                    {
                        // Get product images for each product
                        var productsWithImages = new List<object>();

                        foreach (var product in productList)
                        {
                            // Get product images
                            var images = new List<object>();

                            // Get detailed product info to access images
                            var productDetails = await _productServicesDAL.GetProductDetailsById(product.ProductId);

                            if (productDetails != null && productDetails.ProductPicturesMappings != null && productDetails.ProductPicturesMappings.Any())
                            {
                                // First, try to find the primary image
                                var primaryMapping = productDetails.ProductPicturesMappings
                                    .FirstOrDefault(m => m.IsPrimary == true);

                                if (primaryMapping != null)
                                {
                                    // Add the primary image first
                                    images.Add(new
                                    {
                                        AttachmentName = primaryMapping.Picture?.AttachmentName ?? "Primary Product Image",
                                        AttachmentURL = primaryMapping.Picture?.AttachmentUrl ?? $"/images/products/{product.ProductId}/{primaryMapping.PictureId}.jpg",
                                        IsPrimary = true
                                    });
                                }

                                // Then add other images (up to a total of 2 images)
                                foreach (var mapping in productDetails.ProductPicturesMappings
                                    .Where(m => m.IsPrimary != true)
                                    .Take(images.Count < 2 ? 2 - images.Count : 0))
                                {
                                    images.Add(new
                                    {
                                        AttachmentName = mapping.Picture?.AttachmentName ?? "Product Image",
                                        AttachmentURL = mapping.Picture?.AttachmentUrl ?? $"/images/products/{product.ProductId}/{mapping.PictureId}.jpg",
                                        IsPrimary = false
                                    });
                                }
                            }

                            // If no images found, add a placeholder
                            if (images.Count == 0)
                            {
                                images.Add(new
                                {
                                    AttachmentName = "No Image",
                                    AttachmentURL = "/images/no-image.jpg",
                                    IsPrimary = true
                                });
                            }

                            // Get category info
                            var categoryMappings = await _productServicesDAL.ReadProductCategoriesById(product.ProductId);
                            int categoryID = 0;
                            string categoryName = "";

                            if (categoryMappings != null && categoryMappings.Count > 0)
                            {
                                var firstCategory = categoryMappings.FirstOrDefault();
                                if (firstCategory != null)
                                {
                                    categoryID = firstCategory.CategoryId;
                                    categoryName = firstCategory.Category?.Name ?? "";
                                }
                            }

                            // Add product with images and category info
                            productsWithImages.Add(new
                            {
                                ProductId = product.ProductId,
                                ProductName = product.ProductName,
                                Price = product.Price,
                                StockQuantity = product.StockQuantity,
                                IsActive = product.IsActive,
                                IsDiscountAllowed = product.IsDiscountAllowed,
                                CategoryID = categoryID,
                                CategoryName = categoryName,
                                Rating = 0, // Default value
                                TotalRecords = product.TotalRecords,
                                ProductImagesJson = images
                            });
                        }

                        data = JsonConvert.SerializeObject(productsWithImages);
                    }
                    else
                    {
                        data = "[]";
                    }
                }
                catch (Exception ex)
                {
                    // Log the specific error from this section
                    Console.WriteLine($"Error in product retrieval: {ex.Message}");
                    throw; // Re-throw to be caught by the outer catch block
                }

                #region result
                result.Data = data;
                result.StatusCode = 200;
                result.StatusMessage = "Ok";
                result.ErrorMessage = String.Empty;
                apiActionResult = new APIActionResult(result);
                #endregion
            }
            catch (Exception ex)
            {
                #region log error
                await _commonServicesDAL.LogRunTimeExceptionDAL(ex.Message, ex.StackTrace, ex.StackTrace);
                #endregion

                result.StatusCode = 501;
                result.StatusMessage = "Error";
                result.ErrorMessage = $"Error: {ex.Message}. Stack trace: {ex.StackTrace}";
                apiActionResult = new APIActionResult(result);
            }

            return apiActionResult;
        }

        private T GetParameterValue<T>(Dictionary<string, object> parameters, string key, T defaultValue)
        {
            if (parameters != null && parameters.ContainsKey(key) && parameters[key] != null)
            {
                try
                {
                    if (typeof(T) == typeof(int))
                    {
                        return (T)(object)Convert.ToInt32(parameters[key]);
                    }
                    else if (typeof(T) == typeof(string))
                    {
                        return (T)(object)parameters[key].ToString();
                    }
                    else
                    {
                        return (T)parameters[key];
                    }
                }
                catch
                {
                    return defaultValue;
                }
            }
            return defaultValue;
        }


    }
}
