"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/page.tsx":
/*!***********************************!*\
  !*** ./app/product/[id]/page.tsx ***!
  \***********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/head */ \"(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\");\n/* harmony import */ var next_head__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_head__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// SEO Head Component\nfunction ProductSEOHead(param) {\n    let { product } = param;\n    var _product_DiscountPrice;\n    if (!product) return null;\n    const metaTitle = product.MetaTitle || \"\".concat(product.ProductName, \" - Medical Equipment\");\n    const metaDescription = product.MetaDescription || product.ShortDescription || \"Buy \".concat(product.ProductName, \" at the best price. High-quality medical equipment with fast delivery.\");\n    const metaKeywords = product.MetaKeywords || \"\".concat(product.ProductName, \", medical equipment, healthcare, \").concat(product.CategoryName || 'medical supplies');\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_head__WEBPACK_IMPORTED_MODULE_4___default()), {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"title\", {\n                children: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 121,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"keywords\",\n                content: metaKeywords\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 122,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:type\",\n                content: \"product\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 125,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:url\",\n                content:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 126,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                property: \"og:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:card\",\n                content: \"summary_large_image\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:title\",\n                content: metaTitle\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 131,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:description\",\n                content: metaDescription\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            product.ProductImagesJson && product.ProductImagesJson.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"twitter:image\",\n                content: constructImageUrl(product.ProductImagesJson[0].AttachmentURL)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 134,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:amount\",\n                content: ((_product_DiscountPrice = product.DiscountPrice) === null || _product_DiscountPrice === void 0 ? void 0 : _product_DiscountPrice.toString()) || product.Price.toString()\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 136,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:price:currency\",\n                content: \"USD\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 137,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:availability\",\n                content: product.StockQuantity > 0 ? \"in stock\" : \"out of stock\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 138,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"meta\", {\n                name: \"product:condition\",\n                content: \"new\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"link\", {\n                rel: \"canonical\",\n                href:  true ? window.location.href : 0\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 140,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 119,\n        columnNumber: 5\n    }, this);\n}\n_c = ProductSEOHead;\n// Helper function to construct image URL (moved outside component for reuse)\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetails(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetails.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetails.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetails.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetails.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetails.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Using the exact configuration provided\n            const data = JSON.stringify({\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            });\n            const config = {\n                method: \"post\",\n                maxBodyLength: Number.POSITIVE_INFINITY,\n                url: \"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\",\n                headers: {\n                    Accept: \"application/json\",\n                    \"Content-Type\": \"application/json\"\n                },\n                data: data\n            };\n            const response = await axios__WEBPACK_IMPORTED_MODULE_16__[\"default\"].request(config);\n            console.log(\"Product detail API response:\", response.data);\n            if (response.data && response.data.data) {\n                try {\n                    // Parse the response data\n                    const parsedData = JSON.parse(response.data.data);\n                    console.log(\"Parsed product data:\", parsedData);\n                    if (parsedData) {\n                        // The API might return an array with one item or a single object\n                        const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                        if (productData) {\n                            // Ensure AttributesJson is properly parsed if it's a string\n                            if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                try {\n                                    productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                } catch (e) {\n                                    console.error('Error parsing AttributesJson:', e);\n                                    productData.AttributesJson = [];\n                                }\n                            } else if (!productData.AttributesJson) {\n                                productData.AttributesJson = [];\n                            }\n                            console.log('Product data with attributes:', productData);\n                            setProduct(productData);\n                            // Set active image\n                            if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                            }\n                            // Handle comma-separated video links\n                            if (productData.VideoLink) {\n                                console.log(\"Video links found:\", productData.VideoLink);\n                                const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                setVideoLinks(processedLinks);\n                                setSelectedVideoIndex(0);\n                            }\n                            // Set initial quantity based on product minimum order quantity\n                            if (productData.OrderMinimumQuantity > 0) {\n                                setQuantity(productData.OrderMinimumQuantity);\n                            }\n                        } else {\n                            setError(\"No product data found\");\n                        }\n                    } else {\n                        setError(\"Invalid product data format\");\n                    }\n                } catch (parseError) {\n                    setError(\"Error parsing product data\");\n                    console.error(\"Error parsing product data:\", parseError);\n                }\n            } else {\n                setError(\"No data in API response\");\n            }\n        } catch (error) {\n            setError(\"Error fetching product details\");\n            console.error(\"Error fetching product:\", error);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructImageUrl = (attachmentUrl)=>{\n        if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n        if (attachmentUrl.startsWith(\"http\")) {\n            return attachmentUrl;\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n        return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetails.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetails.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetails.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 368,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 375,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 382,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 361,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 390,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 398,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 397,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 407,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 408,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 406,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 359,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetails.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetails.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetails.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetails.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 456,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 461,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 454,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetails.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetails.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetails.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetails.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetails.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetails.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 551,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 559,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 550,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 572,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 576,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 582,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 583,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 585,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 584,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 581,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 600,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 599,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 604,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 607,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 606,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 605,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 610,\n                            columnNumber: 11\n                        }, this),\n                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                            href: \"/products/category/\".concat(product.CategoryID),\n                                            children: product.CategoryName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 615,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 614,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 613,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 618,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_6__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 622,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 621,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 598,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 597,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_13__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 630,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 629,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 646,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 644,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 656,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 643,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 667,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 666,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex items-center\",\n                                children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 676,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"In Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 677,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 675,\n                                            columnNumber: 17\n                                        }, this),\n                                        product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"(\",\n                                                product.StockQuantity,\n                                                \" available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 680,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-red-600\",\n                                    children: \"Out of Stock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 684,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 672,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 690,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 691,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 696,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 708,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 707,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                                    lineNumber: 724,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                            lineNumber: 718,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                        lineNumber: 717,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 706,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 699,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 695,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 693,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 738,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 689,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-4 text-sm font-medium\",\n                                                children: \"Quantity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 745,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: decrementQuantity,\n                                                        className: getButtonStyles('decrement'),\n                                                        disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                        \"aria-label\": \"Decrease quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 754,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 753,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 747,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 758,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: incrementQuantity,\n                                                        className: getButtonStyles('increment'),\n                                                        disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                        \"aria-label\": \"Increase quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                                lineNumber: 771,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                            lineNumber: 770,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 760,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 746,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Min: \",\n                                                    product.OrderMinimumQuantity\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 777,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Max: \",\n                                                    Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 783,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 744,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                            lineNumber: 792,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 791,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 743,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                );\n                                                // Show success toast\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                            } catch (error) {\n                                                console.error('Error adding to cart:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 851,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 853,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 855,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 806,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    // Add to wishlist\n                                                    wishlist.addToWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error('Error updating wishlist:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.error('Failed to update wishlist. Please try again.');\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 889,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 891,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 896,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 859,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_9__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 922,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 923,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 903,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 804,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 flex flex-wrap gap-2\",\n                                children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_8__.Badge, {\n                                        variant: \"secondary\",\n                                        className: \"text-xs\",\n                                        children: keyword.trim()\n                                    }, index, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 931,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-12 p-4 bg-gray-50 rounded-lg border\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium mb-2\",\n                                        children: \"About This Product\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 940,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 941,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 939,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                        lineNumber: 637,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 627,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsList, {\n                            className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 957,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"specifications\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 958,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Shipping & Returns\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 960,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose max-w-none\",\n                                dangerouslySetInnerHTML: {\n                                    __html: product.FullDescription\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 965,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No description available for this product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 963,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"specifications\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_12__.ProductSpecifications, {\n                                attributes: product.AttributesJson,\n                                className: \"bg-white rounded-lg\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 976,\n                                columnNumber: 15\n                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-gray-500 italic\",\n                                children: \"No specifications available for this product.\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 981,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 974,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    1,\n                                                    2,\n                                                    3,\n                                                    4,\n                                                    5\n                                                ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                    }, star, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 990,\n                                                        columnNumber: 21\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 988,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm text-gray-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1001,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    \" out of 5\",\n                                                    product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: [\n                                                            \" • \",\n                                                            product.TotalReviews,\n                                                            \" review\",\n                                                            product.TotalReviews !== 1 ? 's' : ''\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1003,\n                                                        columnNumber: 21\n                                                    }, this) : null\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1000,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 987,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-gray-50 p-4 rounded-lg\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                className: \"font-medium mb-2\",\n                                                children: \"Customer Reviews\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1009,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8 text-gray-500\",\n                                                    children: \"Reviews will be displayed here\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                    lineNumber: 1013,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1011,\n                                                columnNumber: 19\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-center py-8\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-500 mb-4\",\n                                                        children: \"No reviews yet\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1019,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        variant: \"outline\",\n                                                        size: \"sm\",\n                                                        children: \"Be the first to review\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1020,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1018,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1008,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 986,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 985,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_7__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 p-6 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600\",\n                                        children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1031,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                        className: \"h-6 w-6 text-primary mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1034,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"Fast Delivery\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1035,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: [\n                                                            \"Estimated delivery time: \",\n                                                            product.EstimatedShippingDays || '3-5',\n                                                            \" business days\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1036,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1033,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"p-4 border rounded-lg\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                        className: \"h-6 w-6 text-primary mb-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1040,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"font-medium mb-1\",\n                                                        children: \"Easy Returns\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1041,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-500\",\n                                                        children: \"Hassle-free returns within 30 days\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                        lineNumber: 1042,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 1039,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 1032,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                                lineNumber: 1030,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                            lineNumber: 1029,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                    lineNumber: 950,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n                lineNumber: 949,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 595,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetails, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_10__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_11__.useWishlist\n    ];\n});\n_c1 = ProductDetails;\nfunction ProductPage() {\n    _s1();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const id = params.id;\n    if (!id) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n            error: \"Product ID not found\",\n            retry: ()=>{}\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n            lineNumber: 1062,\n            columnNumber: 12\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductDetails, {\n        productId: id\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\page.tsx\",\n        lineNumber: 1065,\n        columnNumber: 10\n    }, this);\n}\n_s1(ProductPage, \"+jVsTcECDRo3yq2d7EQxlN9Ixog=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams\n    ];\n});\n_c2 = ProductPage;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"ProductSEOHead\");\n$RefreshReg$(_c1, \"ProductDetails\");\n$RefreshReg$(_c2, \"ProductPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js":
/*!***************************************************************!*\
  !*** ./node_modules/next/dist/client/components/noop-head.js ***!
  \***************************************************************/
/***/ ((module, exports, __webpack_require__) => {

eval(__webpack_require__.ts("\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nObject.defineProperty(exports, \"default\", ({\n    enumerable: true,\n    get: function() {\n        return NoopHead;\n    }\n}));\nfunction NoopHead() {\n    return null;\n}\n_c = NoopHead;\nif ((typeof exports.default === 'function' || typeof exports.default === 'object' && exports.default !== null) && typeof exports.default.__esModule === 'undefined') {\n    Object.defineProperty(exports.default, '__esModule', {\n        value: true\n    });\n    Object.assign(exports.default, exports);\n    module.exports = exports.default;\n} //# sourceMappingURL=noop-head.js.map\nvar _c;\n$RefreshReg$(_c, \"NoopHead\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY2xpZW50L2NvbXBvbmVudHMvbm9vcC1oZWFkLmpzIiwibWFwcGluZ3MiOiI7Ozs7MkNBQUE7OztlQUF3QkE7OztBQUFUO0lBQ2IsT0FBTztBQUNUO0tBRndCQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFx5aHlhc29mdFxcRG93bmxvYWRzXFxlY1xcLk5FVCA4IFZlcnNpb24gLSBMYXRlc3RcXHNyY1xcY2xpZW50XFxjb21wb25lbnRzXFxub29wLWhlYWQudHN4Il0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIE5vb3BIZWFkKCkge1xuICByZXR1cm4gbnVsbFxufVxuIl0sIm5hbWVzIjpbIk5vb3BIZWFkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/client/components/noop-head.js\n"));

/***/ })

});