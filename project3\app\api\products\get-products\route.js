import axios from 'axios';
import { NextResponse } from 'next/server';

export async function POST(request) {
  try {
    // Parse the request body
    const body = await request.json();
    
    // Log the incoming request for debugging
    console.log('Incoming request to get-products:', body);
    
    // Prepare the request parameters for the external API
    let recordValueJson = '[]';
    
    // If productIds are provided, create a filter for them
    if (body.productIds && Array.isArray(body.productIds) && body.productIds.length > 0) {
      recordValueJson = JSON.stringify(body.productIds.map(id => ({
        columnName: 'ProductId',
        columnValue: id,
        operator: 'Equal',
        logicalOperator: 'OR'
      })));
    }
    
    const data = {
      requestParameters: {
        SearchTerm: body.requestParameters?.SearchTerm || '',
        CategoryID: body.requestParameters?.CategoryID || null,
        TagID: null,
        ManufacturerID: null,
        MinPrice: body.requestParameters?.MinPrice || null,
        MaxPrice: body.requestParameters?.MaxPrice || null,
        Rating: null,
        OrderByColumnName: body.requestParameters?.OrderByColumnName !== undefined ? body.requestParameters.OrderByColumnName : 0,
        PageNo: body.requestParameters?.PageNo || 1,
        PageSize: body.requestParameters?.PageSize || 1000, // Get more items for wishlist
        recordValueJson: recordValueJson,
        producttypeId: body.requestParameters?.producttypeId || null,
      },
    };

    // Make the request to the external API
    const response = await axios.post(
      'https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-all-products', // Corrected external API URL
      data,
      {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      }
    );

    // Log the response for debugging
    console.log('API response status:', response.status);
    console.log('API response data structure:', Object.keys(response.data));

    // Parse and log product IDs for debugging
    if (response.data && response.data.data) {
      try {
        const products = JSON.parse(response.data.data);
        if (Array.isArray(products) && products.length > 0) {
          // Log the structure of the first product to understand the data format
          console.log('First product structure:', Object.keys(products[0]));
          console.log('First product sample:', products[0]);

          const productIds = products.map(p => p.ProductID).slice(0, 10); // Log first 10 product IDs (note: ProductID not ProductId)
          console.log('Available Product IDs (first 10):', productIds);
          console.log('Total products found:', products.length);
        } else {
          console.log('No products found in response');
        }
      } catch (parseError) {
        console.log('Error parsing products data:', parseError);
      }
    }

    // Return the response data
    return NextResponse.json(response.data);
  } catch (error) {
    console.error('Error fetching products:', error.message);
    console.error('Request that caused error:', body);
    
    if (error.response) {
      console.error('Error response status:', error.response.status);
      console.error('Error response data:', error.response.data);
    }
    
    return NextResponse.json(
      { error: 'Failed to fetch products', message: error.message },
      { status: error.response?.status || 500 }
    );
  }
}