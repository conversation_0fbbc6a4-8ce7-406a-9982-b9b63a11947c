"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/product/[id]/page",{

/***/ "(app-pages-browser)/./app/product/[id]/product-details-client.tsx":
/*!*****************************************************!*\
  !*** ./app/product/[id]/product-details-client.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! axios */ \"(app-pages-browser)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-left.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/check.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/shopping-cart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/share-2.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/truck.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Award,Check,Heart,RotateCcw,Share2,ShoppingCart,Star,Truck!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/rotate-ccw.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/breadcrumb */ \"(app-pages-browser)/./components/ui/breadcrumb.tsx\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var sonner__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! sonner */ \"(app-pages-browser)/./node_modules/sonner/dist/index.mjs\");\n/* harmony import */ var _contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/contexts/cart-context */ \"(app-pages-browser)/./contexts/cart-context.tsx\");\n/* harmony import */ var _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/contexts/wishlist-context */ \"(app-pages-browser)/./contexts/wishlist-context.tsx\");\n/* harmony import */ var _components_products_product_specifications__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/products/product-specifications */ \"(app-pages-browser)/./components/products/product-specifications.tsx\");\n/* harmony import */ var _components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/products/product-media-gallery */ \"(app-pages-browser)/./components/products/product-media-gallery.tsx\");\n/* harmony import */ var _product_loading__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./product-loading */ \"(app-pages-browser)/./app/product/[id]/product-loading.tsx\");\n/* harmony import */ var _product_error__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./product-error */ \"(app-pages-browser)/./app/product/[id]/product-error.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to construct image URL\nconst constructImageUrl = (attachmentUrl)=>{\n    if (!attachmentUrl) return \"/placeholder.svg?height=400&width=400\";\n    if (attachmentUrl.startsWith(\"http\")) {\n        return attachmentUrl;\n    }\n    const baseUrl = \"https://admin.codemedicalapps.com\";\n    const normalizedAttachmentUrl = attachmentUrl.startsWith(\"/\") ? attachmentUrl : \"/\".concat(attachmentUrl);\n    return \"\".concat(baseUrl).concat(normalizedAttachmentUrl);\n};\nfunction ProductDetailsClient(param) {\n    let { productId } = param;\n    var _product_Rating;\n    _s();\n    const cart = (0,_contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__.useCart)();\n    const wishlist = (0,_contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__.useWishlist)();\n    const [product, setProduct] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [quantity, setQuantity] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [activeImage, setActiveImage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [videoLinks, setVideoLinks] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedVideoIndex, setSelectedVideoIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [addingToCart, setAddingToCart] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [addingToWishlist, setAddingToWishlist] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"description\");\n    const [isAnimating, setIsAnimating] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [animationType, setAnimationType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedAttributes, setSelectedAttributes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        \"ProductDetailsClient.useState\": ()=>{\n            // Initialize with first option selected for each attribute if none selected\n            const initial = {};\n            if (product === null || product === void 0 ? void 0 : product.AttributesJson) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useState\": (attr)=>{\n                        const key = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        initial[key] = true; // Select first option by default\n                    }\n                }[\"ProductDetailsClient.useState\"]);\n            }\n            return initial;\n        }\n    }[\"ProductDetailsClient.useState\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProductDetailsClient.useEffect\": ()=>{\n            fetchProduct();\n        }\n    }[\"ProductDetailsClient.useEffect\"], [\n        productId\n    ]);\n    const fetchProduct = async ()=>{\n        setLoading(true);\n        setError(null);\n        try {\n            // Try direct API call first, then fallback to proxy if CORS issues\n            const requestBody = {\n                requestParameters: {\n                    ProductId: Number.parseInt(productId, 10),\n                    recordValueJson: \"[]\"\n                }\n            };\n            console.log(\"Fetching product with ID:\", productId, \"Request body:\", requestBody);\n            let response;\n            try {\n                // Try direct API call first\n                response = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(\"https://admin.codemedicalapps.com/api/v1/dynamic/dataoperation/get-product_detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Direct API response:\", response.data);\n            } catch (directError) {\n                console.log(\"Direct API failed, trying proxy route:\", directError);\n                // Fallback to proxy route\n                response = await axios__WEBPACK_IMPORTED_MODULE_14__[\"default\"].post(\"/api/product-detail\", requestBody, {\n                    headers: {\n                        Accept: \"application/json\",\n                        \"Content-Type\": \"application/json\"\n                    }\n                });\n                console.log(\"Proxy API response:\", response.data);\n            }\n            if (response.data) {\n                // Check if response.data has a nested data property (from API proxy)\n                const responseData = response.data.data ? response.data : response.data;\n                if (responseData && responseData.data) {\n                    try {\n                        // Parse the response data\n                        const parsedData = JSON.parse(responseData.data);\n                        console.log(\"Parsed product data:\", parsedData);\n                        if (parsedData) {\n                            // The API might return an array with one item or a single object\n                            const productData = Array.isArray(parsedData) ? parsedData[0] : parsedData;\n                            if (productData) {\n                                // Ensure AttributesJson is properly parsed if it's a string\n                                if (productData.AttributesJson && typeof productData.AttributesJson === 'string') {\n                                    try {\n                                        productData.AttributesJson = JSON.parse(productData.AttributesJson);\n                                    } catch (e) {\n                                        console.error('Error parsing AttributesJson:', e);\n                                        productData.AttributesJson = [];\n                                    }\n                                } else if (!productData.AttributesJson) {\n                                    productData.AttributesJson = [];\n                                }\n                                console.log('Product data with attributes:', productData);\n                                setProduct(productData);\n                                // Set active image\n                                if (productData.ProductImagesJson && productData.ProductImagesJson.length > 0) {\n                                    const primaryImage = productData.ProductImagesJson.find((img)=>img.IsPrimary) || productData.ProductImagesJson[0];\n                                    setActiveImage(constructImageUrl(primaryImage.AttachmentURL));\n                                }\n                                // Handle comma-separated video links\n                                if (productData.VideoLink) {\n                                    console.log(\"Video links found:\", productData.VideoLink);\n                                    const links = productData.VideoLink.split(\",\").map((link)=>link.trim());\n                                    const processedLinks = links.map((link)=>constructVideoUrl(link));\n                                    setVideoLinks(processedLinks);\n                                    setSelectedVideoIndex(0);\n                                }\n                                // Set initial quantity based on product minimum order quantity\n                                if (productData.OrderMinimumQuantity > 0) {\n                                    setQuantity(productData.OrderMinimumQuantity);\n                                }\n                            } else {\n                                console.error(\"No product data found in parsed response\");\n                                setError(\"Product not found\");\n                            }\n                        } else {\n                            console.error(\"Invalid product data format - parsedData is null/undefined\");\n                            setError(\"Invalid product data format\");\n                        }\n                    } catch (parseError) {\n                        console.error(\"Error parsing product data:\", parseError, \"Raw data:\", responseData.data);\n                        setError(\"Error parsing product data\");\n                    }\n                } else {\n                    console.error(\"No data property in API response:\", response.data);\n                    setError(\"No data in API response\");\n                }\n            } else {\n                console.error(\"Empty response from API\");\n                setError(\"Empty response from server\");\n            }\n        } catch (error) {\n            console.error(\"Error fetching product:\", error);\n            // More detailed error handling\n            if (error.response) {\n                var _error_response_data;\n                // Server responded with error status\n                console.error(\"Server error:\", error.response.status, error.response.data);\n                setError(\"Server error: \".concat(error.response.status, \" - \").concat(((_error_response_data = error.response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.message) || 'Unknown error'));\n            } else if (error.request) {\n                // Request was made but no response received\n                console.error(\"Network error:\", error.request);\n                setError(\"Network error - please check your connection\");\n            } else {\n                // Something else happened\n                console.error(\"Request setup error:\", error.message);\n                setError(\"Error: \".concat(error.message));\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const constructVideoUrl = (videoLink)=>{\n        if (!videoLink) return \"\";\n        if (videoLink.includes('youtube.com') || videoLink.includes('youtu.be')) {\n            return videoLink;\n        }\n        // For MP4 videos, use a proxy URL to handle CORS\n        if (videoLink.startsWith('http')) {\n            return \"/api/video-proxy?url=\".concat(encodeURIComponent(videoLink));\n        }\n        const baseUrl = \"https://admin.codemedicalapps.com\";\n        const normalizedVideoLink = videoLink.startsWith('/') ? videoLink : \"/\".concat(videoLink);\n        return \"/api/video-proxy?url=\".concat(encodeURIComponent(\"\".concat(baseUrl).concat(normalizedVideoLink)));\n    };\n    // Group attributes by ProductAttributeID\n    const groupedAttributes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[groupedAttributes]\": ()=>{\n            if (!(product === null || product === void 0 ? void 0 : product.AttributesJson)) return {};\n            return product.AttributesJson.reduce({\n                \"ProductDetailsClient.useMemo[groupedAttributes]\": (groups, attr)=>{\n                    const groupId = attr.ProductAttributeID;\n                    if (!groups[groupId]) {\n                        groups[groupId] = [];\n                    }\n                    groups[groupId].push(attr);\n                    return groups;\n                }\n            }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], {});\n        }\n    }[\"ProductDetailsClient.useMemo[groupedAttributes]\"], [\n        product === null || product === void 0 ? void 0 : product.AttributesJson\n    ]);\n    // Handle attribute selection with conditional behavior\n    const handleAttributeChange = (attr, isChecked, isRadioGroup)=>{\n        setSelectedAttributes((prev)=>{\n            const newState = {\n                ...prev\n            };\n            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n            // For radio groups, uncheck all other attributes in the same group\n            if (isRadioGroup && isChecked) {\n                Object.keys(prev).forEach((key)=>{\n                    if (key.startsWith(\"\".concat(attr.ProductAttributeID, \"_\")) && key !== attrKey) {\n                        newState[key] = false;\n                    }\n                });\n            }\n            // Set the selected attribute\n            // For checkboxes, toggle the state\n            // For radio buttons, always set to true (since we already unset others if needed)\n            newState[attrKey] = isRadioGroup ? true : !prev[attrKey];\n            return newState;\n        });\n    };\n    // Render price with all price-related information\n    const renderPrice = ()=>{\n        if (!product) return null;\n        const showDiscount = product.DiscountPrice && product.DiscountPrice < product.Price;\n        const adjustedPrice = calculateAdjustedPrice();\n        const showAdjustedPrice = adjustedPrice !== product.Price && adjustedPrice !== (product.DiscountPrice || product.Price);\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"mb-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-baseline gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-3xl font-bold text-primary\",\n                            children: [\n                                \"$\",\n                                showDiscount ? (product.DiscountPrice || 0).toFixed(2) : adjustedPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 352,\n                            columnNumber: 11\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 358,\n                            columnNumber: 13\n                        }, this),\n                        showAdjustedPrice && !showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"text-lg text-gray-400 line-through\",\n                            children: [\n                                \"$\",\n                                product.Price.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 365,\n                            columnNumber: 13\n                        }, this),\n                        showDiscount && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 px-2 py-1 bg-red-100 text-red-800 text-xs font-medium rounded\",\n                            children: [\n                                Math.round((product.Price - (product.DiscountPrice || 0)) / product.Price * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 372,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 351,\n                    columnNumber: 9\n                }, this),\n                product.PriceIQD && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-lg font-medium text-gray-600\",\n                    children: [\n                        product.PriceIQD.toLocaleString(),\n                        \" IQD\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 380,\n                    columnNumber: 11\n                }, this),\n                product.PointNo && product.PointNo > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-2\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-amber-100 text-amber-800\",\n                        children: [\n                            \"Earn \",\n                            product.PointNo,\n                            \" points\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 387,\n                    columnNumber: 11\n                }, this),\n                product.OldPrice && product.OldPrice > product.Price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-1 text-sm text-gray-500\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"line-through\",\n                            children: [\n                                \"$\",\n                                product.OldPrice.toFixed(2)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 397,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"ml-2 text-green-600\",\n                            children: [\n                                Math.round((product.OldPrice - (product.DiscountPrice || product.Price)) / product.OldPrice * 100),\n                                \"% OFF\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 398,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 396,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 349,\n            columnNumber: 7\n        }, this);\n    };\n    // Calculate adjusted price based on selected attributes\n    const calculateAdjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": ()=>{\n            if (!product) return 0;\n            let adjustedPrice = product.Price;\n            if (product.AttributesJson && product.AttributesJson.length > 0) {\n                product.AttributesJson.forEach({\n                    \"ProductDetailsClient.useCallback[calculateAdjustedPrice]\": (attr)=>{\n                        const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                        if (selectedAttributes[attrKey] && typeof attr.PriceAdjustment === 'number' && typeof attr.PriceAdjustmentType === 'number') {\n                            switch(attr.PriceAdjustmentType){\n                                case 1:\n                                    adjustedPrice += attr.PriceAdjustment;\n                                    break;\n                                case 2:\n                                    adjustedPrice += product.Price * attr.PriceAdjustment / 100;\n                                    break;\n                            }\n                        }\n                    }\n                }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"]);\n            }\n            return Math.max(0, adjustedPrice); // Ensure price doesn't go below 0\n        }\n    }[\"ProductDetailsClient.useCallback[calculateAdjustedPrice]\"], [\n        product,\n        selectedAttributes\n    ]);\n    const adjustedPrice = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[adjustedPrice]\": ()=>calculateAdjustedPrice()\n    }[\"ProductDetailsClient.useMemo[adjustedPrice]\"], [\n        calculateAdjustedPrice\n    ]);\n    // Render product badges\n    const renderBadges = ()=>{\n        if (!product) return null;\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"absolute top-4 left-4 z-10 flex flex-col gap-2\",\n            children: [\n                product.IsDiscountAllowed && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-red-500 hover:bg-red-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"SALE\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 444,\n                    columnNumber: 11\n                }, this),\n                product.MarkAsNew && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                    className: \"bg-green-500 hover:bg-green-600 text-white text-sm font-bold px-3 py-1\",\n                    children: \"NEW\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 449,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 442,\n            columnNumber: 7\n        }, this);\n    };\n    // Combine images and videos into a single media array for the gallery\n    const mediaItems = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"ProductDetailsClient.useMemo[mediaItems]\": ()=>{\n            var _product_ProductImagesJson;\n            const items = [];\n            // Add product images\n            if (product === null || product === void 0 ? void 0 : (_product_ProductImagesJson = product.ProductImagesJson) === null || _product_ProductImagesJson === void 0 ? void 0 : _product_ProductImagesJson.length) {\n                product.ProductImagesJson.forEach({\n                    \"ProductDetailsClient.useMemo[mediaItems]\": (img)=>{\n                        items.push({\n                            type: 'image',\n                            url: constructImageUrl(img.AttachmentURL),\n                            alt: (product === null || product === void 0 ? void 0 : product.ProductName) || 'Product image',\n                            thumbnail: constructImageUrl(img.AttachmentURL)\n                        });\n                    }\n                }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            }\n            // Add videos\n            videoLinks.forEach({\n                \"ProductDetailsClient.useMemo[mediaItems]\": (videoUrl, index)=>{\n                    items.push({\n                        type: 'video',\n                        url: videoUrl,\n                        alt: \"\".concat((product === null || product === void 0 ? void 0 : product.ProductName) || 'Product', \" - Video \").concat(index + 1),\n                        thumbnail: activeImage || '' // Use the active image as video thumbnail\n                    });\n                }\n            }[\"ProductDetailsClient.useMemo[mediaItems]\"]);\n            return items;\n        }\n    }[\"ProductDetailsClient.useMemo[mediaItems]\"], [\n        product,\n        videoLinks,\n        activeImage\n    ]);\n    const animateCounter = (type)=>{\n        setAnimationType(type);\n        setIsAnimating(true);\n        setTimeout(()=>setIsAnimating(false), 300);\n    };\n    const incrementQuantity = ()=>{\n        if (product) {\n            const maxQuantity = product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity;\n            if (quantity < maxQuantity) {\n                setQuantity((prev)=>prev + 1);\n                animateCounter('increment');\n            } else {\n                // Visual feedback when max quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Maximum quantity of \".concat(maxQuantity, \" reached\"));\n            }\n        }\n    };\n    const decrementQuantity = ()=>{\n        if (product) {\n            const minQuantity = product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1;\n            if (quantity > minQuantity) {\n                setQuantity((prev)=>prev - 1);\n                animateCounter('decrement');\n            } else {\n                // Visual feedback when min quantity is reached\n                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.info(\"Minimum quantity is \".concat(minQuantity));\n            }\n        }\n    };\n    // Dynamic button styles based on state\n    const getButtonStyles = (type)=>{\n        const baseStyles = 'flex items-center justify-center w-10 h-10 rounded-full transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';\n        const disabledStyles = 'bg-gray-100 text-gray-400 cursor-not-allowed';\n        if (type === 'increment') {\n            const isMax = product && quantity >= (product.OrderMaximumQuantity > 0 ? Math.min(product.OrderMaximumQuantity, product.StockQuantity) : product.StockQuantity);\n            return \"\".concat(baseStyles, \" \").concat(isMax ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        } else {\n            const isMin = product && quantity <= (product.OrderMinimumQuantity > 0 ? product.OrderMinimumQuantity : 1);\n            return \"\".concat(baseStyles, \" \").concat(isMin ? disabledStyles : 'bg-primary text-white hover:bg-primary/90 focus:ring-primary/50');\n        }\n    };\n    // Counter display with animation\n    const CounterDisplay = ()=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"relative flex items-center justify-center w-16\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"text-lg font-medium transition-all duration-200 \".concat(isAnimating ? 'scale-125 text-primary' : 'scale-100'),\n                    children: quantity\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 539,\n                    columnNumber: 7\n                }, this),\n                isAnimating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                    className: \"absolute text-xs font-bold text-primary transition-all duration-200 \".concat(animationType === 'increment' ? '-top-6' : 'top-6'),\n                    children: animationType === 'increment' ? '+1' : '-1'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 547,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 538,\n            columnNumber: 5\n        }, this);\n    // Early return if product is not loaded yet\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_loading__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 560,\n            columnNumber: 12\n        }, this);\n    }\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_product_error__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n            error: error,\n            retry: fetchProduct\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 564,\n            columnNumber: 12\n        }, this);\n    }\n    if (!product) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4 py-8 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-2xl font-bold mb-4\",\n                    children: \"Product Not Found\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 570,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mb-6\",\n                    children: \"The product you are looking for could not be found.\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 571,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    href: \"/products\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                className: \"mr-2 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 574,\n                                columnNumber: 13\n                            }, this),\n                            \"View All Products\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 572,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n            lineNumber: 569,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"container mx-auto py-8 px-4 w-full max-w-[1200px] overflow-x-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.Breadcrumb, {\n                className: \"mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbList, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/\",\n                                    children: \"Home\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 589,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 588,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 587,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 592,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                asChild: true,\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                    href: \"/products\",\n                                    children: \"Products\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 595,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 594,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 593,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 598,\n                            columnNumber: 11\n                        }, this),\n                        product.CategoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbLink, {\n                                        asChild: true,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                            href: \"/products/category/\".concat(product.CategoryID),\n                                            children: product.CategoryName\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 603,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 602,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 601,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbSeparator, {}, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 606,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbItem, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_breadcrumb__WEBPACK_IMPORTED_MODULE_4__.BreadcrumbPage, {\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 610,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 609,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 586,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 585,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:w-1/2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_media_gallery__WEBPACK_IMPORTED_MODULE_11__.ProductMediaGallery, {\n                            media: mediaItems,\n                            className: \"w-full rounded-lg overflow-hidden\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 618,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 617,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"md:w-1/2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-xl font-bold mb-2\",\n                                children: product.ProductName\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 626,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center mb-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex\",\n                                        children: [\n                                            ...Array(5)\n                                        ].map((_, i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"w-4 h-4 \".concat(i < Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                            }, i, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 632,\n                                                columnNumber: 17\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 630,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-sm text-gray-500 ml-2\",\n                                        children: [\n                                            \"(\",\n                                            product.Rating || 0,\n                                            \") \",\n                                            product.TotalReviews || 0,\n                                            \" reviews\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 642,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 629,\n                                columnNumber: 11\n                            }, this),\n                            renderPrice(),\n                            product.ShortDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"prose prose-sm max-w-none mb-6\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: product.ShortDescription\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 653,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 652,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-4 flex items-center\",\n                                children: product.StockQuantity > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center text-green-600\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-4 w-4 mr-1\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 662,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-sm font-medium\",\n                                                    children: \"In Stock\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 663,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 661,\n                                            columnNumber: 17\n                                        }, this),\n                                        product.DisplayStockQuantity && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-sm text-gray-500 ml-2\",\n                                            children: [\n                                                \"(\",\n                                                product.StockQuantity,\n                                                \" available)\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 666,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-sm font-medium text-red-600\",\n                                    children: \"Out of Stock\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 670,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 658,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6 border-t border-gray-200 pt-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                                        children: \"Product Details\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 676,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-600 mb-4\",\n                                        children: \"Choose your preferences from the options below.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 677,\n                                        columnNumber: 13\n                                    }, this),\n                                    Object.entries(groupedAttributes).length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-6\",\n                                        children: Object.entries(groupedAttributes).map((param)=>{\n                                            let [groupId, attributes] = param;\n                                            var _attributes_, _attributes_1;\n                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                        className: \"text-sm font-medium text-gray-700\",\n                                                        children: [\n                                                            ((_attributes_ = attributes[0]) === null || _attributes_ === void 0 ? void 0 : _attributes_.DisplayName) || ((_attributes_1 = attributes[0]) === null || _attributes_1 === void 0 ? void 0 : _attributes_1.AttributeName),\n                                                            \":\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 682,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"space-y-2 pl-4\",\n                                                        children: attributes.map((attr)=>{\n                                                            const attrKey = \"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID);\n                                                            const isSelected = !!selectedAttributes[attrKey];\n                                                            const isRadioGroup = attributes.length > 1;\n                                                            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-start\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"flex items-center h-5\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                                                            type: isRadioGroup ? \"radio\" : \"checkbox\",\n                                                                            id: \"attr-\".concat(attrKey),\n                                                                            name: \"attr-group-\".concat(groupId),\n                                                                            className: \"h-4 w-4 \".concat(isRadioGroup ? 'rounded-full' : 'rounded', \" border-gray-300 text-primary focus:ring-primary\"),\n                                                                            checked: isSelected,\n                                                                            onChange: (e)=>handleAttributeChange(attr, e.target.checked, isRadioGroup)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 694,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 693,\n                                                                        columnNumber: 29\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                        className: \"ml-3 text-sm\",\n                                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                                                            htmlFor: \"attr-\".concat(attrKey),\n                                                                            className: \"font-medium \".concat(isSelected ? 'text-primary' : 'text-gray-700'),\n                                                                            children: [\n                                                                                attr.AttributeValueText,\n                                                                                (attr.PriceAdjustment || attr.PriceAdjustment === 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"ml-2 text-sm font-normal text-green-600\",\n                                                                                    children: [\n                                                                                        \"(\",\n                                                                                        attr.PriceAdjustmentType === 1 ? '+' : '',\n                                                                                        \"$\",\n                                                                                        attr.PriceAdjustment,\n                                                                                        \" \",\n                                                                                        attr.PriceAdjustmentType === 2 ? '%' : '',\n                                                                                        \")\"\n                                                                                    ]\n                                                                                }, void 0, true, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                                    lineNumber: 710,\n                                                                                    columnNumber: 35\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                            lineNumber: 704,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                        lineNumber: 703,\n                                                                        columnNumber: 29\n                                                                    }, this)\n                                                                ]\n                                                            }, attrKey, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 692,\n                                                                columnNumber: 27\n                                                            }, this);\n                                                        })\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 685,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, \"attr-group-\".concat(groupId), true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 681,\n                                                columnNumber: 19\n                                            }, this);\n                                        })\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 679,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"No additional product details available.\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 724,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 675,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"mr-4 text-sm font-medium\",\n                                                children: \"Quantity:\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 731,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center space-x-1\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: decrementQuantity,\n                                                        className: getButtonStyles('decrement'),\n                                                        disabled: quantity <= (product.OrderMinimumQuantity || 1),\n                                                        \"aria-label\": \"Decrease quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 740,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 739,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 733,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(CounterDisplay, {}, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 744,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: incrementQuantity,\n                                                        className: getButtonStyles('increment'),\n                                                        disabled: product.OrderMaximumQuantity > 0 ? quantity >= Math.min(product.OrderMaximumQuantity, product.StockQuantity) : quantity >= product.StockQuantity,\n                                                        \"aria-label\": \"Increase quantity\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                            xmlns: \"http://www.w3.org/2000/svg\",\n                                                            className: \"h-5 w-5\",\n                                                            viewBox: \"0 0 20 20\",\n                                                            fill: \"currentColor\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                fillRule: \"evenodd\",\n                                                                d: \"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z\",\n                                                                clipRule: \"evenodd\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                                lineNumber: 757,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 756,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 746,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 732,\n                                                columnNumber: 15\n                                            }, this),\n                                            product.OrderMinimumQuantity > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Min: \",\n                                                    product.OrderMinimumQuantity\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 763,\n                                                columnNumber: 17\n                                            }, this),\n                                            product.OrderMaximumQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-3 text-xs text-gray-500\",\n                                                children: [\n                                                    \"Max: \",\n                                                    Math.min(product.OrderMaximumQuantity, product.StockQuantity)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 769,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 730,\n                                        columnNumber: 13\n                                    }, this),\n                                    product.DisplayStockQuantity && product.StockQuantity > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mt-2 w-full bg-gray-200 rounded-full h-2.5\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-green-500 h-2.5 rounded-full transition-all duration-500 ease-out\",\n                                            style: {\n                                                width: \"\".concat(Math.min(100, quantity / product.StockQuantity * 100), \"%\"),\n                                                backgroundColor: quantity > product.StockQuantity * 0.8 ? '#ef4444' : '#10b981'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 778,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 777,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 729,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-wrap gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex-1 flex items-center justify-center gap-2 py-3 px-4 rounded-md bg-primary text-white hover:bg-primary/90 disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: product.StockQuantity <= 0 || addingToCart,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToCart(true);\n                                            try {\n                                                // Get the first product image or use a placeholder\n                                                const productImage = product.ProductImagesJson && product.ProductImagesJson.length > 0 ? constructImageUrl(product.ProductImagesJson[0].AttachmentURL) : '/placeholder.jpg';\n                                                // Get selected attributes\n                                                const selectedAttrs = (product.AttributesJson || []).filter((attr)=>selectedAttributes[\"\".concat(attr.ProductAttributeID, \"_\").concat(attr.AttributeValueID)]);\n                                                // Add to cart using the cart context with attributes and adjusted price\n                                                cart.addToCart({\n                                                    id: product.ProductId,\n                                                    name: product.ProductName,\n                                                    price: product.DiscountPrice || product.Price,\n                                                    discountPrice: product.DiscountPrice,\n                                                    image: productImage,\n                                                    originalPrice: product.Price\n                                                }, quantity, selectedAttrs, product.PriceIQD // Pass IQD price as the fourth parameter\n                                                );\n                                                // Show success toast\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(quantity, \" \\xd7 \").concat(product.ProductName, \" added to your cart\"));\n                                            } catch (error) {\n                                                console.error('Error adding to cart:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error(\"Failed to add product to cart. Please try again.\");\n                                            } finally{\n                                                setAddingToCart(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToCart ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 837,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 839,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: addingToCart ? \"Adding...\" : \"Add to Cart\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 841,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 792,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground disabled:opacity-50 disabled:pointer-events-none\",\n                                        disabled: addingToWishlist,\n                                        onClick: ()=>{\n                                            if (!product) return;\n                                            setAddingToWishlist(true);\n                                            try {\n                                                // Check if product is already in wishlist\n                                                const isAlreadyInWishlist = wishlist.isInWishlist(product.ProductId);\n                                                if (isAlreadyInWishlist) {\n                                                    // Remove from wishlist if already there\n                                                    wishlist.removeFromWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" removed from wishlist\"));\n                                                } else {\n                                                    // Add to wishlist\n                                                    wishlist.addToWishlist(product.ProductId);\n                                                    sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"\".concat(product.ProductName, \" added to wishlist\"));\n                                                }\n                                            } catch (error) {\n                                                console.error('Error updating wishlist:', error);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.error('Failed to update wishlist. Please try again.');\n                                            } finally{\n                                                setAddingToWishlist(false);\n                                            }\n                                        },\n                                        children: [\n                                            addingToWishlist ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-5 w-5 border-2 border-current border-t-transparent rounded-full animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 875,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                className: \"h-5 w-5\",\n                                                fill: product && wishlist.isInWishlist(product.ProductId) ? \"currentColor\" : \"none\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 877,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: addingToWishlist ? \"Updating...\" : product && wishlist.isInWishlist(product.ProductId) ? \"Remove\" : \"Wishlist\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 882,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 845,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"button\",\n                                        className: \"flex items-center justify-center gap-2 py-3 px-4 rounded-md border border-input bg-transparent hover:bg-accent hover:text-accent-foreground\",\n                                        onClick: ()=>{\n                                            if (navigator.share) {\n                                                navigator.share({\n                                                    title: (product === null || product === void 0 ? void 0 : product.MetaTitle) || (product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    text: (product === null || product === void 0 ? void 0 : product.MetaDescription) || \"Check out this product: \".concat(product === null || product === void 0 ? void 0 : product.ProductName),\n                                                    url: window.location.href\n                                                }).catch((err)=>console.error(\"Error sharing:\", err));\n                                            } else {\n                                                // Fallback - copy to clipboard\n                                                navigator.clipboard.writeText(window.location.href);\n                                                sonner__WEBPACK_IMPORTED_MODULE_7__.toast.success(\"Product link copied to clipboard\");\n                                            }\n                                        },\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 908,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"sr-only md:not-sr-only md:inline\",\n                                                children: \"Share\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 909,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 889,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 790,\n                                columnNumber: 11\n                            }, this),\n                            product.MetaKeywords && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-4 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3\",\n                                        children: \"Product Tags\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 916,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex flex-wrap gap-2\",\n                                        children: product.MetaKeywords.split(\",\").map((keyword, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                variant: \"secondary\",\n                                                className: \"text-xs bg-white/70 hover:bg-white transition-colors\",\n                                                children: keyword.trim()\n                                            }, index, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 919,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 917,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 915,\n                                columnNumber: 13\n                            }, this),\n                            product.MetaDescription && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-6 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg border border-green-200\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"font-medium text-gray-900 mb-3 flex items-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                className: \"h-5 w-5 text-green-600 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                lineNumber: 931,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"About This Product\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 930,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-700 leading-relaxed\",\n                                        children: product.MetaDescription\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                        lineNumber: 934,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 929,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                        lineNumber: 625,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 615,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.Tabs, {\n                    defaultValue: \"description\",\n                    className: \"w-full\",\n                    value: activeTab,\n                    onValueChange: setActiveTab,\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsList, {\n                            className: \"grid w-full grid-cols-2 sm:grid-cols-4 mb-6 gap-2\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"description\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Description\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 950,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"specifications\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Specifications\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 951,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"reviews\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Reviews\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 952,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsTrigger, {\n                                    value: \"shipping\",\n                                    className: \"shadow-sm hover:shadow transition-shadow\",\n                                    children: \"Shipping & Returns\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 953,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 949,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"description\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: product.FullDescription ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"prose max-w-none\",\n                                    dangerouslySetInnerHTML: {\n                                        __html: product.FullDescription\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 959,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-500 italic\",\n                                    children: \"No description available for this product.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 964,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 957,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 956,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"specifications\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: product.AttributesJson && product.AttributesJson.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_products_product_specifications__WEBPACK_IMPORTED_MODULE_10__.ProductSpecifications, {\n                                    attributes: product.AttributesJson,\n                                    className: \"bg-white rounded-lg\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 972,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-500 italic\",\n                                    children: \"No specifications available for this product.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 977,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 970,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 969,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"reviews\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-6\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col sm:flex-row sm:items-center gap-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3,\n                                                        4,\n                                                        5\n                                                    ].map((star)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                            className: \"w-6 h-6 \".concat(star <= Math.floor(product.Rating || 0) ? \"text-yellow-400 fill-yellow-400\" : \"text-gray-300\")\n                                                        }, star, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 988,\n                                                            columnNumber: 23\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 986,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm text-gray-600\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-medium\",\n                                                            children: ((_product_Rating = product.Rating) === null || _product_Rating === void 0 ? void 0 : _product_Rating.toFixed(1)) || '0.0'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 999,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \" out of 5\",\n                                                        product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                \" • \",\n                                                                product.TotalReviews,\n                                                                \" review\",\n                                                                product.TotalReviews !== 1 ? 's' : ''\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1001,\n                                                            columnNumber: 23\n                                                        }, this) : null\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 998,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 985,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"bg-gray-50 p-4 rounded-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                    className: \"font-medium mb-2\",\n                                                    children: \"Customer Reviews\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1007,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.TotalReviews ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"space-y-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-center py-8 text-gray-500\",\n                                                        children: \"Reviews will be displayed here\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                        lineNumber: 1011,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1009,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-center py-8\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-gray-500 mb-4\",\n                                                            children: \"No reviews yet\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1017,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            children: \"Be the first to review\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1018,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1016,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1006,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 984,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 983,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 982,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_5__.TabsContent, {\n                            value: \"shipping\",\n                            className: \"mt-4 bg-white rounded-lg shadow-sm\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"space-y-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600\",\n                                            children: \"Shipping and delivery information will be provided during checkout based on your location.\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1031,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-4 mt-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-6 w-6 text-primary mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1034,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Fast Delivery\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1035,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: [\n                                                                \"Estimated delivery time: \",\n                                                                product.EstimatedShippingDays || '3-5',\n                                                                \" business days\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1036,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1033,\n                                                    columnNumber: 19\n                                                }, this),\n                                                product.IsReturnAble && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"p-4 border rounded-lg\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Award_Check_Heart_RotateCcw_Share2_ShoppingCart_Star_Truck_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-6 w-6 text-primary mb-2\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1040,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                            className: \"font-medium mb-1\",\n                                                            children: \"Easy Returns\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1041,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm text-gray-500\",\n                                                            children: \"Hassle-free returns within 30 days\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                            lineNumber: 1042,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                                    lineNumber: 1039,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                            lineNumber: 1032,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                    lineNumber: 1030,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                                lineNumber: 1029,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                            lineNumber: 1028,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                    lineNumber: 943,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n                lineNumber: 942,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Downloads\\\\ec\\\\.NET 8 Version - Latest\\\\project\\\\codemedical\\\\project3\\\\app\\\\product\\\\[id]\\\\product-details-client.tsx\",\n        lineNumber: 583,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductDetailsClient, \"Wn9PVs6A/vYrKTZXf/IyUUUPGxE=\", false, function() {\n    return [\n        _contexts_cart_context__WEBPACK_IMPORTED_MODULE_8__.useCart,\n        _contexts_wishlist_context__WEBPACK_IMPORTED_MODULE_9__.useWishlist\n    ];\n});\n_c = ProductDetailsClient;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProductDetailsClient);\nvar _c;\n$RefreshReg$(_c, \"ProductDetailsClient\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/product/[id]/product-details-client.tsx\n"));

/***/ })

});